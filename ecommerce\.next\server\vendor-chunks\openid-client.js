/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/openid-client";
exports.ids = ["vendor-chunks/openid-client"];
exports.modules = {

/***/ "(rsc)/./node_modules/openid-client/lib/client.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/client.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nconst { inspect } = __webpack_require__(/*! util */ \"util\");\nconst stdhttp = __webpack_require__(/*! http */ \"http\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { strict: assert } = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst { URL, URLSearchParams } = __webpack_require__(/*! url */ \"url\");\nconst jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nconst tokenHash = __webpack_require__(/*! oidc-token-hash */ \"(rsc)/./node_modules/oidc-token-hash/lib/index.js\");\nconst isKeyObject = __webpack_require__(/*! ./helpers/is_key_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\");\nconst decodeJWT = __webpack_require__(/*! ./helpers/decode_jwt */ \"(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\");\nconst base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst defaults = __webpack_require__(/*! ./helpers/defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./helpers/www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\nconst { assertSigningAlgValuesSupport, assertIssuerConfiguration } = __webpack_require__(/*! ./helpers/assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst isPlainObject = __webpack_require__(/*! ./helpers/is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst { random } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { CLOCK_TOLERANCE } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst { keystores } = __webpack_require__(/*! ./helpers/weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst KeyStore = __webpack_require__(/*! ./helpers/keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { authenticatedPost, resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\nconst { queryKeyStore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\nconst DeviceFlowHandle = __webpack_require__(/*! ./device_flow_handle */ \"(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\");\nconst [major, minor] = process.version.slice(1).split('.').map((str)=>parseInt(str, 10));\nconst rsaPssParams = major >= 17 || major === 16 && minor >= 9;\nconst retryAttempt = Symbol();\nconst skipNonceCheck = Symbol();\nconst skipMaxAgeCheck = Symbol();\nfunction pickCb(input) {\n    return pick(input, 'access_token', 'code', 'error_description', 'error_uri', 'error', 'expires_in', 'id_token', 'iss', 'response', 'session_state', 'state', 'token_type');\n}\nfunction authorizationHeaderValue(token, tokenType = 'Bearer') {\n    return `${tokenType} ${token}`;\n}\nfunction getSearchParams(input) {\n    const parsed = url.parse(input);\n    if (!parsed.search) return {};\n    return querystring.parse(parsed.search.substring(1));\n}\nfunction verifyPresence(payload, jwt, prop) {\n    if (payload[prop] === undefined) {\n        throw new RPError({\n            message: `missing required JWT property ${prop}`,\n            jwt\n        });\n    }\n}\nfunction authorizationParams(params) {\n    const authParams = {\n        client_id: this.client_id,\n        scope: 'openid',\n        response_type: resolveResponseType.call(this),\n        redirect_uri: resolveRedirectUri.call(this),\n        ...params\n    };\n    Object.entries(authParams).forEach(([key, value])=>{\n        if (value === null || value === undefined) {\n            delete authParams[key];\n        } else if (key === 'claims' && typeof value === 'object') {\n            authParams[key] = JSON.stringify(value);\n        } else if (key === 'resource' && Array.isArray(value)) {\n            authParams[key] = value;\n        } else if (typeof value !== 'string') {\n            authParams[key] = String(value);\n        }\n    });\n    return authParams;\n}\nfunction getKeystore(jwks) {\n    if (!isPlainObject(jwks) || !Array.isArray(jwks.keys) || jwks.keys.some((k)=>!isPlainObject(k) || !('kty' in k))) {\n        throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n    return KeyStore.fromJWKS(jwks, {\n        onlyPrivate: true\n    });\n}\n// if an OP doesnt support client_secret_basic but supports client_secret_post, use it instead\n// this is in place to take care of most common pitfalls when first using discovered Issuers without\n// the support for default values defined by Discovery 1.0\nfunction checkBasicSupport(client, properties) {\n    try {\n        const supported = client.issuer.token_endpoint_auth_methods_supported;\n        if (!supported.includes(properties.token_endpoint_auth_method)) {\n            if (supported.includes('client_secret_post')) {\n                properties.token_endpoint_auth_method = 'client_secret_post';\n            }\n        }\n    } catch (err) {}\n}\nfunction handleCommonMistakes(client, metadata, properties) {\n    if (!metadata.token_endpoint_auth_method) {\n        // if no explicit value was provided\n        checkBasicSupport(client, properties);\n    }\n    // :fp: c'mon people... RTFM\n    if (metadata.redirect_uri) {\n        if (metadata.redirect_uris) {\n            throw new TypeError('provide a redirect_uri or redirect_uris, not both');\n        }\n        properties.redirect_uris = [\n            metadata.redirect_uri\n        ];\n        delete properties.redirect_uri;\n    }\n    if (metadata.response_type) {\n        if (metadata.response_types) {\n            throw new TypeError('provide a response_type or response_types, not both');\n        }\n        properties.response_types = [\n            metadata.response_type\n        ];\n        delete properties.response_type;\n    }\n}\nfunction getDefaultsForEndpoint(endpoint, issuer, properties) {\n    if (!issuer[`${endpoint}_endpoint`]) return;\n    const tokenEndpointAuthMethod = properties.token_endpoint_auth_method;\n    const tokenEndpointAuthSigningAlg = properties.token_endpoint_auth_signing_alg;\n    const eam = `${endpoint}_endpoint_auth_method`;\n    const easa = `${endpoint}_endpoint_auth_signing_alg`;\n    if (properties[eam] === undefined && properties[easa] === undefined) {\n        if (tokenEndpointAuthMethod !== undefined) {\n            properties[eam] = tokenEndpointAuthMethod;\n        }\n        if (tokenEndpointAuthSigningAlg !== undefined) {\n            properties[easa] = tokenEndpointAuthSigningAlg;\n        }\n    }\n}\nclass BaseClient {\n    #metadata;\n    #issuer;\n    #aadIssValidation;\n    #additionalAuthorizedParties;\n    constructor(issuer, aadIssValidation, metadata = {}, jwks, options){\n        this.#metadata = new Map();\n        this.#issuer = issuer;\n        this.#aadIssValidation = aadIssValidation;\n        if (typeof metadata.client_id !== 'string' || !metadata.client_id) {\n            throw new TypeError('client_id is required');\n        }\n        const properties = {\n            grant_types: [\n                'authorization_code'\n            ],\n            id_token_signed_response_alg: 'RS256',\n            authorization_signed_response_alg: 'RS256',\n            response_types: [\n                'code'\n            ],\n            token_endpoint_auth_method: 'client_secret_basic',\n            ...this.fapi1() ? {\n                grant_types: [\n                    'authorization_code',\n                    'implicit'\n                ],\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                response_types: [\n                    'code id_token'\n                ],\n                tls_client_certificate_bound_access_tokens: true,\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...this.fapi2() ? {\n                id_token_signed_response_alg: 'PS256',\n                authorization_signed_response_alg: 'PS256',\n                token_endpoint_auth_method: undefined\n            } : undefined,\n            ...metadata\n        };\n        if (this.fapi()) {\n            switch(properties.token_endpoint_auth_method){\n                case 'self_signed_tls_client_auth':\n                case 'tls_client_auth':\n                    break;\n                case 'private_key_jwt':\n                    if (!jwks) {\n                        throw new TypeError('jwks is required');\n                    }\n                    break;\n                case undefined:\n                    throw new TypeError('token_endpoint_auth_method is required');\n                default:\n                    throw new TypeError('invalid or unsupported token_endpoint_auth_method');\n            }\n        }\n        if (this.fapi2()) {\n            if (properties.tls_client_certificate_bound_access_tokens && properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n            if (!properties.tls_client_certificate_bound_access_tokens && !properties.dpop_bound_access_tokens) {\n                throw new TypeError('either tls_client_certificate_bound_access_tokens or dpop_bound_access_tokens must be set to true');\n            }\n        }\n        handleCommonMistakes(this, metadata, properties);\n        assertSigningAlgValuesSupport('token', this.issuer, properties);\n        [\n            'introspection',\n            'revocation'\n        ].forEach((endpoint)=>{\n            getDefaultsForEndpoint(endpoint, this.issuer, properties);\n            assertSigningAlgValuesSupport(endpoint, this.issuer, properties);\n        });\n        Object.entries(properties).forEach(([key, value])=>{\n            this.#metadata.set(key, value);\n            if (!this[key]) {\n                Object.defineProperty(this, key, {\n                    get () {\n                        return this.#metadata.get(key);\n                    },\n                    enumerable: true\n                });\n            }\n        });\n        if (jwks !== undefined) {\n            const keystore = getKeystore.call(this, jwks);\n            keystores.set(this, keystore);\n        }\n        if (options != null && options.additionalAuthorizedParties) {\n            this.#additionalAuthorizedParties = clone(options.additionalAuthorizedParties);\n        }\n        this[CLOCK_TOLERANCE] = 0;\n    }\n    authorizationUrl(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        assertIssuerConfiguration(this.issuer, 'authorization_endpoint');\n        const target = new URL(this.issuer.authorization_endpoint);\n        for (const [name, value] of Object.entries(authorizationParams.call(this, params))){\n            if (Array.isArray(value)) {\n                target.searchParams.delete(name);\n                for (const member of value){\n                    target.searchParams.append(name, member);\n                }\n            } else {\n                target.searchParams.set(name, value);\n            }\n        }\n        // TODO: is the replace needed?\n        return target.href.replace(/\\+/g, '%20');\n    }\n    authorizationPost(params = {}) {\n        if (!isPlainObject(params)) {\n            throw new TypeError('params must be a plain object');\n        }\n        const inputs = authorizationParams.call(this, params);\n        const formInputs = Object.keys(inputs).map((name)=>`<input type=\"hidden\" name=\"${name}\" value=\"${inputs[name]}\"/>`).join('\\n');\n        return `<!DOCTYPE html>\n<head>\n<title>Requesting Authorization</title>\n</head>\n<body onload=\"javascript:document.forms[0].submit()\">\n<form method=\"post\" action=\"${this.issuer.authorization_endpoint}\">\n  ${formInputs}\n</form>\n</body>\n</html>`;\n    }\n    endSessionUrl(params = {}) {\n        assertIssuerConfiguration(this.issuer, 'end_session_endpoint');\n        const { 0: postLogout, length } = this.post_logout_redirect_uris || [];\n        const { post_logout_redirect_uri = length === 1 ? postLogout : undefined } = params;\n        let id_token_hint;\n        ({ id_token_hint, ...params } = params);\n        if (id_token_hint instanceof TokenSet) {\n            if (!id_token_hint.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            id_token_hint = id_token_hint.id_token;\n        }\n        const target = url.parse(this.issuer.end_session_endpoint);\n        const query = defaults(getSearchParams(this.issuer.end_session_endpoint), params, {\n            post_logout_redirect_uri,\n            client_id: this.client_id\n        }, {\n            id_token_hint\n        });\n        Object.entries(query).forEach(([key, value])=>{\n            if (value === null || value === undefined) {\n                delete query[key];\n            }\n        });\n        target.search = null;\n        target.query = query;\n        return url.format(target);\n    }\n    callbackParams(input) {\n        const isIncomingMessage = input instanceof stdhttp.IncomingMessage || input && input.method && input.url;\n        const isString = typeof input === 'string';\n        if (!isString && !isIncomingMessage) {\n            throw new TypeError('#callbackParams only accepts string urls, http.IncomingMessage or a lookalike');\n        }\n        if (isIncomingMessage) {\n            switch(input.method){\n                case 'GET':\n                    return pickCb(getSearchParams(input.url));\n                case 'POST':\n                    if (input.body === undefined) {\n                        throw new TypeError('incoming message body missing, include a body parser prior to this method call');\n                    }\n                    switch(typeof input.body){\n                        case 'object':\n                        case 'string':\n                            if (Buffer.isBuffer(input.body)) {\n                                return pickCb(querystring.parse(input.body.toString('utf-8')));\n                            }\n                            if (typeof input.body === 'string') {\n                                return pickCb(querystring.parse(input.body));\n                            }\n                            return pickCb(input.body);\n                        default:\n                            throw new TypeError('invalid IncomingMessage body object');\n                    }\n                default:\n                    throw new TypeError('invalid IncomingMessage method');\n            }\n        } else {\n            return pickCb(getSearchParams(input));\n        }\n    }\n    async callback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (this.default_max_age && !checks.max_age) {\n            checks.max_age = this.default_max_age;\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            id_token: [\n                'id_token'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                } else {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.id_token) {\n            const tokenset = new TokenSet(params);\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'authorization', checks.max_age, checks.state);\n            if (!params.code) {\n                return tokenset;\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, checks.nonce, 'token', checks.max_age);\n            if (params.session_state) {\n                tokenset.session_state = params.session_state;\n            }\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async oauthCallback(redirectUri, parameters, checks = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let params = pickCb(parameters);\n        if (checks.jarm && !('response' in parameters)) {\n            throw new RPError({\n                message: 'expected a JARM response',\n                checks,\n                params\n            });\n        } else if ('response' in parameters) {\n            const decrypted = await this.decryptJARM(params.response);\n            params = await this.validateJARM(decrypted);\n        }\n        if (params.state && !checks.state) {\n            throw new TypeError('checks.state argument is missing');\n        }\n        if (!params.state && checks.state) {\n            throw new RPError({\n                message: 'state missing from the response',\n                checks,\n                params\n            });\n        }\n        if (checks.state !== params.state) {\n            throw new RPError({\n                printf: [\n                    'state mismatch, expected %s, got: %s',\n                    checks.state,\n                    params.state\n                ],\n                checks,\n                params\n            });\n        }\n        if ('iss' in params) {\n            assertIssuerConfiguration(this.issuer, 'issuer');\n            if (params.iss !== this.issuer.issuer) {\n                throw new RPError({\n                    printf: [\n                        'iss mismatch, expected %s, got: %s',\n                        this.issuer.issuer,\n                        params.iss\n                    ],\n                    params\n                });\n            }\n        } else if (this.issuer.authorization_response_iss_parameter_supported && !('id_token' in params) && !('response' in parameters)) {\n            throw new RPError({\n                message: 'iss missing from the response',\n                params\n            });\n        }\n        if (params.error) {\n            throw new OPError(params);\n        }\n        if (typeof params.id_token === 'string' && params.id_token.length) {\n            throw new RPError({\n                message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                params\n            });\n        }\n        delete params.id_token;\n        const RESPONSE_TYPE_REQUIRED_PARAMS = {\n            code: [\n                'code'\n            ],\n            token: [\n                'access_token',\n                'token_type'\n            ]\n        };\n        if (checks.response_type) {\n            for (const type of checks.response_type.split(' ')){\n                if (type === 'none') {\n                    if (params.code || params.id_token || params.access_token) {\n                        throw new RPError({\n                            message: 'unexpected params encountered for \"none\" response',\n                            checks,\n                            params\n                        });\n                    }\n                }\n                if (RESPONSE_TYPE_REQUIRED_PARAMS[type]) {\n                    for (const param of RESPONSE_TYPE_REQUIRED_PARAMS[type]){\n                        if (!params[param]) {\n                            throw new RPError({\n                                message: `${param} missing from response`,\n                                checks,\n                                params\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if (params.code) {\n            const tokenset = await this.grant({\n                ...exchangeBody,\n                grant_type: 'authorization_code',\n                code: params.code,\n                redirect_uri: redirectUri,\n                code_verifier: checks.code_verifier\n            }, {\n                clientAssertionPayload,\n                DPoP\n            });\n            if (typeof tokenset.id_token === 'string' && tokenset.id_token.length) {\n                throw new RPError({\n                    message: 'id_token detected in the response, you must use client.callback() instead of client.oauthCallback()',\n                    params\n                });\n            }\n            delete tokenset.id_token;\n            return tokenset;\n        }\n        return new TokenSet(params);\n    }\n    async decryptIdToken(token) {\n        if (!this.id_token_encrypted_response_alg) {\n            return token;\n        }\n        let idToken = token;\n        if (idToken instanceof TokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        const expectedAlg = this.id_token_encrypted_response_alg;\n        const expectedEnc = this.id_token_encrypted_response_enc;\n        const result = await this.decryptJWE(idToken, expectedAlg, expectedEnc);\n        if (token instanceof TokenSet) {\n            token.id_token = result;\n            return token;\n        }\n        return result;\n    }\n    async validateJWTUserinfo(body) {\n        const expectedAlg = this.userinfo_signed_response_alg;\n        return this.validateJWT(body, expectedAlg, []);\n    }\n    async decryptJARM(response) {\n        if (!this.authorization_encrypted_response_alg) {\n            return response;\n        }\n        const expectedAlg = this.authorization_encrypted_response_alg;\n        const expectedEnc = this.authorization_encrypted_response_enc;\n        return this.decryptJWE(response, expectedAlg, expectedEnc);\n    }\n    async decryptJWTUserinfo(body) {\n        if (!this.userinfo_encrypted_response_alg) {\n            return body;\n        }\n        const expectedAlg = this.userinfo_encrypted_response_alg;\n        const expectedEnc = this.userinfo_encrypted_response_enc;\n        return this.decryptJWE(body, expectedAlg, expectedEnc);\n    }\n    async decryptJWE(jwe, expectedAlg, expectedEnc = 'A128CBC-HS256') {\n        const header = JSON.parse(base64url.decode(jwe.split('.')[0]));\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt: jwe\n            });\n        }\n        if (header.enc !== expectedEnc) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWE enc received, expected %s, got: %s',\n                    expectedEnc,\n                    header.enc\n                ],\n                jwt: jwe\n            });\n        }\n        const getPlaintext = (result)=>new TextDecoder().decode(result.plaintext);\n        let plaintext;\n        if (expectedAlg.match(/^(?:RSA|ECDH)/)) {\n            const keystore = await keystores.get(this);\n            const protectedHeader = jose.decodeProtectedHeader(jwe);\n            for (const key of keystore.all({\n                ...protectedHeader,\n                use: 'enc'\n            })){\n                plaintext = await jose.compactDecrypt(jwe, await key.keyObject(protectedHeader.alg)).then(getPlaintext, ()=>{});\n                if (plaintext) break;\n            }\n        } else {\n            plaintext = await jose.compactDecrypt(jwe, this.secretForAlg(expectedAlg === 'dir' ? expectedEnc : expectedAlg)).then(getPlaintext, ()=>{});\n        }\n        if (!plaintext) {\n            throw new RPError({\n                message: 'failed to decrypt JWE',\n                jwt: jwe\n            });\n        }\n        return plaintext;\n    }\n    async validateIdToken(tokenSet, nonce, returnedBy, maxAge, state) {\n        let idToken = tokenSet;\n        const expectedAlg = this.id_token_signed_response_alg;\n        const isTokenSet = idToken instanceof TokenSet;\n        if (isTokenSet) {\n            if (!idToken.id_token) {\n                throw new TypeError('id_token not present in TokenSet');\n            }\n            idToken = idToken.id_token;\n        }\n        idToken = String(idToken);\n        const timestamp = now();\n        const { protected: header, payload, key } = await this.validateJWT(idToken, expectedAlg);\n        if (typeof maxAge === 'number' || maxAge !== skipMaxAgeCheck && this.require_auth_time) {\n            if (!payload.auth_time) {\n                throw new RPError({\n                    message: 'missing required JWT property auth_time',\n                    jwt: idToken\n                });\n            }\n            if (typeof payload.auth_time !== 'number') {\n                throw new RPError({\n                    message: 'JWT auth_time claim must be a JSON numeric value',\n                    jwt: idToken\n                });\n            }\n        }\n        if (typeof maxAge === 'number' && payload.auth_time + maxAge < timestamp - this[CLOCK_TOLERANCE]) {\n            throw new RPError({\n                printf: [\n                    'too much time has elapsed since the last End-User authentication, max_age %i, auth_time: %i, now %i',\n                    maxAge,\n                    payload.auth_time,\n                    timestamp - this[CLOCK_TOLERANCE]\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                auth_time: payload.auth_time,\n                jwt: idToken\n            });\n        }\n        if (nonce !== skipNonceCheck && (payload.nonce || nonce !== undefined) && payload.nonce !== nonce) {\n            throw new RPError({\n                printf: [\n                    'nonce mismatch, expected %s, got: %s',\n                    nonce,\n                    payload.nonce\n                ],\n                jwt: idToken\n            });\n        }\n        if (returnedBy === 'authorization') {\n            if (!payload.at_hash && tokenSet.access_token) {\n                throw new RPError({\n                    message: 'missing required property at_hash',\n                    jwt: idToken\n                });\n            }\n            if (!payload.c_hash && tokenSet.code) {\n                throw new RPError({\n                    message: 'missing required property c_hash',\n                    jwt: idToken\n                });\n            }\n            if (this.fapi1()) {\n                if (!payload.s_hash && (tokenSet.state || state)) {\n                    throw new RPError({\n                        message: 'missing required property s_hash',\n                        jwt: idToken\n                    });\n                }\n            }\n            if (payload.s_hash) {\n                if (!state) {\n                    throw new TypeError('cannot verify s_hash, \"checks.state\" property not provided');\n                }\n                try {\n                    tokenHash.validate({\n                        claim: 's_hash',\n                        source: 'state'\n                    }, payload.s_hash, state, header.alg, key.jwk && key.jwk.crv);\n                } catch (err) {\n                    throw new RPError({\n                        message: err.message,\n                        jwt: idToken\n                    });\n                }\n            }\n        }\n        if (this.fapi() && payload.iat < timestamp - 3600) {\n            throw new RPError({\n                printf: [\n                    'JWT issued too far in the past, now %i, iat %i',\n                    timestamp,\n                    payload.iat\n                ],\n                now: timestamp,\n                tolerance: this[CLOCK_TOLERANCE],\n                iat: payload.iat,\n                jwt: idToken\n            });\n        }\n        if (tokenSet.access_token && payload.at_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'at_hash',\n                    source: 'access_token'\n                }, payload.at_hash, tokenSet.access_token, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        if (tokenSet.code && payload.c_hash !== undefined) {\n            try {\n                tokenHash.validate({\n                    claim: 'c_hash',\n                    source: 'code'\n                }, payload.c_hash, tokenSet.code, header.alg, key.jwk && key.jwk.crv);\n            } catch (err) {\n                throw new RPError({\n                    message: err.message,\n                    jwt: idToken\n                });\n            }\n        }\n        return tokenSet;\n    }\n    async validateJWT(jwt, expectedAlg, required = [\n        'iss',\n        'sub',\n        'aud',\n        'exp',\n        'iat'\n    ]) {\n        const isSelfIssued = this.issuer.issuer === 'https://self-issued.me';\n        const timestamp = now();\n        let header;\n        let payload;\n        try {\n            ({ header, payload } = decodeJWT(jwt, {\n                complete: true\n            }));\n        } catch (err) {\n            throw new RPError({\n                printf: [\n                    'failed to decode JWT (%s: %s)',\n                    err.name,\n                    err.message\n                ],\n                jwt\n            });\n        }\n        if (header.alg !== expectedAlg) {\n            throw new RPError({\n                printf: [\n                    'unexpected JWT alg received, expected %s, got: %s',\n                    expectedAlg,\n                    header.alg\n                ],\n                jwt\n            });\n        }\n        if (isSelfIssued) {\n            required = [\n                ...required,\n                'sub_jwk'\n            ];\n        }\n        required.forEach(verifyPresence.bind(undefined, payload, jwt));\n        if (payload.iss !== undefined) {\n            let expectedIss = this.issuer.issuer;\n            if (this.#aadIssValidation) {\n                expectedIss = this.issuer.issuer.replace('{tenantid}', payload.tid);\n            }\n            if (payload.iss !== expectedIss) {\n                throw new RPError({\n                    printf: [\n                        'unexpected iss value, expected %s, got: %s',\n                        expectedIss,\n                        payload.iss\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.iat !== undefined) {\n            if (typeof payload.iat !== 'number') {\n                throw new RPError({\n                    message: 'JWT iat claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n        }\n        if (payload.nbf !== undefined) {\n            if (typeof payload.nbf !== 'number') {\n                throw new RPError({\n                    message: 'JWT nbf claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (payload.nbf > timestamp + this[CLOCK_TOLERANCE]) {\n                throw new RPError({\n                    printf: [\n                        'JWT not active yet, now %i, nbf %i',\n                        timestamp + this[CLOCK_TOLERANCE],\n                        payload.nbf\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    nbf: payload.nbf,\n                    jwt\n                });\n            }\n        }\n        if (payload.exp !== undefined) {\n            if (typeof payload.exp !== 'number') {\n                throw new RPError({\n                    message: 'JWT exp claim must be a JSON numeric value',\n                    jwt\n                });\n            }\n            if (timestamp - this[CLOCK_TOLERANCE] >= payload.exp) {\n                throw new RPError({\n                    printf: [\n                        'JWT expired, now %i, exp %i',\n                        timestamp - this[CLOCK_TOLERANCE],\n                        payload.exp\n                    ],\n                    now: timestamp,\n                    tolerance: this[CLOCK_TOLERANCE],\n                    exp: payload.exp,\n                    jwt\n                });\n            }\n        }\n        if (payload.aud !== undefined) {\n            if (Array.isArray(payload.aud)) {\n                if (payload.aud.length > 1 && !payload.azp) {\n                    throw new RPError({\n                        message: 'missing required JWT property azp',\n                        jwt\n                    });\n                }\n                if (!payload.aud.includes(this.client_id)) {\n                    throw new RPError({\n                        printf: [\n                            'aud is missing the client_id, expected %s to be included in %j',\n                            this.client_id,\n                            payload.aud\n                        ],\n                        jwt\n                    });\n                }\n            } else if (payload.aud !== this.client_id) {\n                throw new RPError({\n                    printf: [\n                        'aud mismatch, expected %s, got: %s',\n                        this.client_id,\n                        payload.aud\n                    ],\n                    jwt\n                });\n            }\n        }\n        if (payload.azp !== undefined) {\n            let additionalAuthorizedParties = this.#additionalAuthorizedParties;\n            if (typeof additionalAuthorizedParties === 'string') {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    additionalAuthorizedParties\n                ];\n            } else if (Array.isArray(additionalAuthorizedParties)) {\n                additionalAuthorizedParties = [\n                    this.client_id,\n                    ...additionalAuthorizedParties\n                ];\n            } else {\n                additionalAuthorizedParties = [\n                    this.client_id\n                ];\n            }\n            if (!additionalAuthorizedParties.includes(payload.azp)) {\n                throw new RPError({\n                    printf: [\n                        'azp mismatch, got: %s',\n                        payload.azp\n                    ],\n                    jwt\n                });\n            }\n        }\n        let keys;\n        if (isSelfIssued) {\n            try {\n                assert(isPlainObject(payload.sub_jwk));\n                const key = await jose.importJWK(payload.sub_jwk, header.alg);\n                assert.equal(key.type, 'public');\n                keys = [\n                    {\n                        keyObject () {\n                            return key;\n                        }\n                    }\n                ];\n            } catch (err) {\n                throw new RPError({\n                    message: 'failed to use sub_jwk claim as an asymmetric JSON Web Key',\n                    jwt\n                });\n            }\n            if (await jose.calculateJwkThumbprint(payload.sub_jwk) !== payload.sub) {\n                throw new RPError({\n                    message: 'failed to match the subject with sub_jwk',\n                    jwt\n                });\n            }\n        } else if (header.alg.startsWith('HS')) {\n            keys = [\n                this.secretForAlg(header.alg)\n            ];\n        } else if (header.alg !== 'none') {\n            keys = await queryKeyStore.call(this.issuer, {\n                ...header,\n                use: 'sig'\n            });\n        }\n        if (!keys && header.alg === 'none') {\n            return {\n                protected: header,\n                payload\n            };\n        }\n        for (const key of keys){\n            const verified = await jose.compactVerify(jwt, key instanceof Uint8Array ? key : await key.keyObject(header.alg)).catch(()=>{});\n            if (verified) {\n                return {\n                    payload,\n                    protected: verified.protectedHeader,\n                    key\n                };\n            }\n        }\n        throw new RPError({\n            message: 'failed to validate JWT signature',\n            jwt\n        });\n    }\n    async refresh(refreshToken, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        let token = refreshToken;\n        if (token instanceof TokenSet) {\n            if (!token.refresh_token) {\n                throw new TypeError('refresh_token not present in TokenSet');\n            }\n            token = token.refresh_token;\n        }\n        const tokenset = await this.grant({\n            ...exchangeBody,\n            grant_type: 'refresh_token',\n            refresh_token: String(token)\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        if (tokenset.id_token) {\n            await this.decryptIdToken(tokenset);\n            await this.validateIdToken(tokenset, skipNonceCheck, 'token', skipMaxAgeCheck);\n            if (refreshToken instanceof TokenSet && refreshToken.id_token) {\n                const expectedSub = refreshToken.claims().sub;\n                const actualSub = tokenset.claims().sub;\n                if (actualSub !== expectedSub) {\n                    throw new RPError({\n                        printf: [\n                            'sub mismatch, expected %s, got: %s',\n                            expectedSub,\n                            actualSub\n                        ],\n                        jwt: tokenset.id_token\n                    });\n                }\n            }\n        }\n        return tokenset;\n    }\n    async requestResource(resourceUrl, accessToken, { method, headers, body, DPoP, tokenType = DPoP ? 'DPoP' : accessToken instanceof TokenSet ? accessToken.token_type : 'Bearer' } = {}, retry) {\n        if (accessToken instanceof TokenSet) {\n            if (!accessToken.access_token) {\n                throw new TypeError('access_token not present in TokenSet');\n            }\n            accessToken = accessToken.access_token;\n        }\n        if (!accessToken) {\n            throw new TypeError('no access token provided');\n        } else if (typeof accessToken !== 'string') {\n            throw new TypeError('invalid access token provided');\n        }\n        const requestOpts = {\n            headers: {\n                Authorization: authorizationHeaderValue(accessToken, tokenType),\n                ...headers\n            },\n            body\n        };\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        const response = await request.call(this, {\n            ...requestOpts,\n            responseType: 'buffer',\n            method,\n            url: resourceUrl\n        }, {\n            accessToken,\n            mTLS,\n            DPoP\n        });\n        const wwwAuthenticate = response.headers['www-authenticate'];\n        if (retry !== retryAttempt && wwwAuthenticate && wwwAuthenticate.toLowerCase().startsWith('dpop ') && parseWwwAuthenticate(wwwAuthenticate).error === 'use_dpop_nonce') {\n            return this.requestResource(resourceUrl, accessToken, {\n                method,\n                headers,\n                body,\n                DPoP,\n                tokenType\n            });\n        }\n        return response;\n    }\n    async userinfo(accessToken, { method = 'GET', via = 'header', tokenType, params, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'userinfo_endpoint');\n        const options = {\n            tokenType,\n            method: String(method).toUpperCase(),\n            DPoP\n        };\n        if (options.method !== 'GET' && options.method !== 'POST') {\n            throw new TypeError('#userinfo() method can only be POST or a GET');\n        }\n        if (via === 'body' && options.method !== 'POST') {\n            throw new TypeError('can only send body on POST');\n        }\n        const jwt = !!(this.userinfo_signed_response_alg || this.userinfo_encrypted_response_alg);\n        if (jwt) {\n            options.headers = {\n                Accept: 'application/jwt'\n            };\n        } else {\n            options.headers = {\n                Accept: 'application/json'\n            };\n        }\n        const mTLS = !!this.tls_client_certificate_bound_access_tokens;\n        let targetUrl;\n        if (mTLS && this.issuer.mtls_endpoint_aliases) {\n            targetUrl = this.issuer.mtls_endpoint_aliases.userinfo_endpoint;\n        }\n        targetUrl = new URL(targetUrl || this.issuer.userinfo_endpoint);\n        if (via === 'body') {\n            options.headers.Authorization = undefined;\n            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n            options.body = new URLSearchParams();\n            options.body.append('access_token', accessToken instanceof TokenSet ? accessToken.access_token : accessToken);\n        }\n        // handle additional parameters, GET via querystring, POST via urlencoded body\n        if (params) {\n            if (options.method === 'GET') {\n                Object.entries(params).forEach(([key, value])=>{\n                    targetUrl.searchParams.append(key, value);\n                });\n            } else if (options.body) {\n                // POST && via body\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            } else {\n                // POST && via header\n                options.body = new URLSearchParams();\n                options.headers['Content-Type'] = 'application/x-www-form-urlencoded';\n                Object.entries(params).forEach(([key, value])=>{\n                    options.body.append(key, value);\n                });\n            }\n        }\n        if (options.body) {\n            options.body = options.body.toString();\n        }\n        const response = await this.requestResource(targetUrl, accessToken, options);\n        let parsed = processResponse(response, {\n            bearer: true\n        });\n        if (jwt) {\n            if (!/^application\\/jwt/.test(response.headers['content-type'])) {\n                throw new RPError({\n                    message: 'expected application/jwt response from the userinfo_endpoint',\n                    response\n                });\n            }\n            const body = response.body.toString();\n            const userinfo = await this.decryptJWTUserinfo(body);\n            if (!this.userinfo_signed_response_alg) {\n                try {\n                    parsed = JSON.parse(userinfo);\n                    assert(isPlainObject(parsed));\n                } catch (err) {\n                    throw new RPError({\n                        message: 'failed to parse userinfo JWE payload as JSON',\n                        jwt: userinfo\n                    });\n                }\n            } else {\n                ({ payload: parsed } = await this.validateJWTUserinfo(userinfo));\n            }\n        } else {\n            try {\n                parsed = JSON.parse(response.body);\n            } catch (err) {\n                Object.defineProperty(err, 'response', {\n                    value: response\n                });\n                throw err;\n            }\n        }\n        if (accessToken instanceof TokenSet && accessToken.id_token) {\n            const expectedSub = accessToken.claims().sub;\n            if (parsed.sub !== expectedSub) {\n                throw new RPError({\n                    printf: [\n                        'userinfo sub mismatch, expected %s, got: %s',\n                        expectedSub,\n                        parsed.sub\n                    ],\n                    body: parsed,\n                    jwt: accessToken.id_token\n                });\n            }\n        }\n        return parsed;\n    }\n    encryptionSecret(len) {\n        const hash = len <= 256 ? 'sha256' : len <= 384 ? 'sha384' : len <= 512 ? 'sha512' : false;\n        if (!hash) {\n            throw new Error('unsupported symmetric encryption key derivation');\n        }\n        return crypto.createHash(hash).update(this.client_secret).digest().slice(0, len / 8);\n    }\n    secretForAlg(alg) {\n        if (!this.client_secret) {\n            throw new TypeError('client_secret is required');\n        }\n        if (/^A(\\d{3})(?:GCM)?KW$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$1, 10));\n        }\n        if (/^A(\\d{3})(?:GCM|CBC-HS(\\d{3}))$/.test(alg)) {\n            return this.encryptionSecret(parseInt(RegExp.$2 || RegExp.$1, 10));\n        }\n        return new TextEncoder().encode(this.client_secret);\n    }\n    async grant(body, { clientAssertionPayload, DPoP } = {}, retry) {\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const response = await authenticatedPost.call(this, 'token', {\n            form: body,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload,\n            DPoP\n        });\n        let responseBody;\n        try {\n            responseBody = processResponse(response);\n        } catch (err) {\n            if (retry !== retryAttempt && err instanceof OPError && err.error === 'use_dpop_nonce') {\n                return this.grant(body, {\n                    clientAssertionPayload,\n                    DPoP\n                }, retryAttempt);\n            }\n            throw err;\n        }\n        return new TokenSet(responseBody);\n    }\n    async deviceAuthorization(params = {}, { exchangeBody, clientAssertionPayload, DPoP } = {}) {\n        assertIssuerConfiguration(this.issuer, 'device_authorization_endpoint');\n        assertIssuerConfiguration(this.issuer, 'token_endpoint');\n        const body = authorizationParams.call(this, {\n            client_id: this.client_id,\n            redirect_uri: null,\n            response_type: null,\n            ...params\n        });\n        const response = await authenticatedPost.call(this, 'device_authorization', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response);\n        return new DeviceFlowHandle({\n            client: this,\n            exchangeBody,\n            clientAssertionPayload,\n            response: responseBody,\n            maxAge: params.max_age,\n            DPoP\n        });\n    }\n    async revoke(token, hint, { revokeBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'revocation_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...revokeBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'revocation', {\n            form\n        }, {\n            clientAssertionPayload\n        });\n        processResponse(response, {\n            body: false\n        });\n    }\n    async introspect(token, hint, { introspectBody, clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'introspection_endpoint');\n        if (hint !== undefined && typeof hint !== 'string') {\n            throw new TypeError('hint must be a string');\n        }\n        const form = {\n            ...introspectBody,\n            token\n        };\n        if (hint) {\n            form.token_type_hint = hint;\n        }\n        const response = await authenticatedPost.call(this, 'introspection', {\n            form,\n            responseType: 'json'\n        }, {\n            clientAssertionPayload\n        });\n        const responseBody = processResponse(response);\n        return responseBody;\n    }\n    static async register(metadata, options = {}) {\n        const { initialAccessToken, jwks, ...clientOptions } = options;\n        assertIssuerConfiguration(this.issuer, 'registration_endpoint');\n        if (jwks !== undefined && !(metadata.jwks || metadata.jwks_uri)) {\n            const keystore = await getKeystore.call(this, jwks);\n            metadata.jwks = keystore.toJWKS();\n        }\n        const response = await request.call(this, {\n            headers: {\n                Accept: 'application/json',\n                ...initialAccessToken ? {\n                    Authorization: authorizationHeaderValue(initialAccessToken)\n                } : undefined\n            },\n            responseType: 'json',\n            json: metadata,\n            url: this.issuer.registration_endpoint,\n            method: 'POST'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201,\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    get metadata() {\n        return clone(Object.fromEntries(this.#metadata.entries()));\n    }\n    static async fromUri(registrationClientUri, registrationAccessToken, jwks, clientOptions) {\n        const response = await request.call(this, {\n            method: 'GET',\n            url: registrationClientUri,\n            responseType: 'json',\n            headers: {\n                Authorization: authorizationHeaderValue(registrationAccessToken),\n                Accept: 'application/json'\n            }\n        });\n        const responseBody = processResponse(response, {\n            bearer: true\n        });\n        return new this(responseBody, jwks, clientOptions);\n    }\n    async requestObject(requestObject = {}, { sign: signingAlgorithm = this.request_object_signing_alg || 'none', encrypt: { alg: eKeyManagement = this.request_object_encryption_alg, enc: eContentEncryption = this.request_object_encryption_enc || 'A128CBC-HS256' } = {} } = {}) {\n        if (!isPlainObject(requestObject)) {\n            throw new TypeError('requestObject must be a plain object');\n        }\n        let signed;\n        let key;\n        const unix = now();\n        const header = {\n            alg: signingAlgorithm,\n            typ: 'oauth-authz-req+jwt'\n        };\n        const payload = JSON.stringify(defaults({}, requestObject, {\n            iss: this.client_id,\n            aud: this.issuer.issuer,\n            client_id: this.client_id,\n            jti: random(),\n            iat: unix,\n            exp: unix + 300,\n            ...this.fapi() ? {\n                nbf: unix\n            } : undefined\n        }));\n        if (signingAlgorithm === 'none') {\n            signed = [\n                base64url.encode(JSON.stringify(header)),\n                base64url.encode(payload),\n                ''\n            ].join('.');\n        } else {\n            const symmetric = signingAlgorithm.startsWith('HS');\n            if (symmetric) {\n                key = this.secretForAlg(signingAlgorithm);\n            } else {\n                const keystore = await keystores.get(this);\n                if (!keystore) {\n                    throw new TypeError(`no keystore present for client, cannot sign using alg ${signingAlgorithm}`);\n                }\n                key = keystore.get({\n                    alg: signingAlgorithm,\n                    use: 'sig'\n                });\n                if (!key) {\n                    throw new TypeError(`no key to sign with found for alg ${signingAlgorithm}`);\n                }\n            }\n            signed = await new jose.CompactSign(new TextEncoder().encode(payload)).setProtectedHeader({\n                ...header,\n                kid: symmetric ? undefined : key.jwk.kid\n            }).sign(symmetric ? key : await key.keyObject(signingAlgorithm));\n        }\n        if (!eKeyManagement) {\n            return signed;\n        }\n        const fields = {\n            alg: eKeyManagement,\n            enc: eContentEncryption,\n            cty: 'oauth-authz-req+jwt'\n        };\n        if (fields.alg.match(/^(RSA|ECDH)/)) {\n            [key] = await queryKeyStore.call(this.issuer, {\n                alg: fields.alg,\n                use: 'enc'\n            }, {\n                allowMulti: true\n            });\n        } else {\n            key = this.secretForAlg(fields.alg === 'dir' ? fields.enc : fields.alg);\n        }\n        return new jose.CompactEncrypt(new TextEncoder().encode(signed)).setProtectedHeader({\n            ...fields,\n            kid: key instanceof Uint8Array ? undefined : key.jwk.kid\n        }).encrypt(key instanceof Uint8Array ? key : await key.keyObject(fields.alg));\n    }\n    async pushedAuthorizationRequest(params = {}, { clientAssertionPayload } = {}) {\n        assertIssuerConfiguration(this.issuer, 'pushed_authorization_request_endpoint');\n        const body = {\n            ...'request' in params ? params : authorizationParams.call(this, params),\n            client_id: this.client_id\n        };\n        const response = await authenticatedPost.call(this, 'pushed_authorization_request', {\n            responseType: 'json',\n            form: body\n        }, {\n            clientAssertionPayload,\n            endpointAuthMethod: 'token'\n        });\n        const responseBody = processResponse(response, {\n            statusCode: 201\n        });\n        if (!('expires_in' in responseBody)) {\n            throw new RPError({\n                message: 'expected expires_in in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.expires_in !== 'number') {\n            throw new RPError({\n                message: 'invalid expires_in value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (!('request_uri' in responseBody)) {\n            throw new RPError({\n                message: 'expected request_uri in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        if (typeof responseBody.request_uri !== 'string') {\n            throw new RPError({\n                message: 'invalid request_uri value in Pushed Authorization Successful Response',\n                response\n            });\n        }\n        return responseBody;\n    }\n    get issuer() {\n        return this.#issuer;\n    }\n    /* istanbul ignore next */ [inspect.custom]() {\n        return `${this.constructor.name} ${inspect(this.metadata, {\n            depth: Infinity,\n            colors: process.stdout.isTTY,\n            compact: false,\n            sorted: true\n        })}`;\n    }\n    fapi() {\n        return this.fapi1() || this.fapi2();\n    }\n    fapi1() {\n        return this.constructor.name === 'FAPI1Client';\n    }\n    fapi2() {\n        return this.constructor.name === 'FAPI2Client';\n    }\n    async validateJARM(response) {\n        const expectedAlg = this.authorization_signed_response_alg;\n        const { payload } = await this.validateJWT(response, expectedAlg, [\n            'iss',\n            'exp',\n            'aud'\n        ]);\n        return pickCb(payload);\n    }\n    /**\n   * @name dpopProof\n   * @api private\n   */ async dpopProof(payload, privateKeyInput, accessToken) {\n        if (!isPlainObject(payload)) {\n            throw new TypeError('payload must be a plain object');\n        }\n        let privateKey;\n        if (isKeyObject(privateKeyInput)) {\n            privateKey = privateKeyInput;\n        } else if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            privateKey = privateKeyInput;\n        } else if (jose.cryptoRuntime === 'node:crypto') {\n            privateKey = crypto.createPrivateKey(privateKeyInput);\n        } else {\n            throw new TypeError('unrecognized crypto runtime');\n        }\n        if (privateKey.type !== 'private') {\n            throw new TypeError('\"DPoP\" option must be a private key');\n        }\n        let alg = determineDPoPAlgorithm.call(this, privateKey, privateKeyInput);\n        if (!alg) {\n            throw new TypeError('could not determine DPoP JWS Algorithm');\n        }\n        return new jose.SignJWT({\n            ath: accessToken ? base64url.encode(crypto.createHash('sha256').update(accessToken).digest()) : undefined,\n            ...payload\n        }).setProtectedHeader({\n            alg,\n            typ: 'dpop+jwt',\n            jwk: await getJwk(privateKey, privateKeyInput)\n        }).setIssuedAt().setJti(random()).sign(privateKey);\n    }\n}\nfunction determineDPoPAlgorithmFromCryptoKey(cryptoKey) {\n    switch(cryptoKey.algorithm.name){\n        case 'Ed25519':\n        case 'Ed448':\n            return 'EdDSA';\n        case 'ECDSA':\n            {\n                switch(cryptoKey.algorithm.namedCurve){\n                    case 'P-256':\n                        return 'ES256';\n                    case 'P-384':\n                        return 'ES384';\n                    case 'P-521':\n                        return 'ES512';\n                    default:\n                        break;\n                }\n                break;\n            }\n        case 'RSASSA-PKCS1-v1_5':\n            return `RS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        case 'RSA-PSS':\n            return `PS${cryptoKey.algorithm.hash.name.slice(4)}`;\n        default:\n            throw new TypeError('unsupported DPoP private key');\n    }\n}\nlet determineDPoPAlgorithm;\nif (jose.cryptoRuntime === 'node:crypto') {\n    determineDPoPAlgorithm = function(privateKey, privateKeyInput) {\n        if (privateKeyInput[Symbol.toStringTag] === 'CryptoKey') {\n            return determineDPoPAlgorithmFromCryptoKey(privateKey);\n        }\n        switch(privateKey.asymmetricKeyType){\n            case 'ed25519':\n            case 'ed448':\n                return 'EdDSA';\n            case 'ec':\n                return determineEcAlgorithm(privateKey, privateKeyInput);\n            case 'rsa':\n            case rsaPssParams && 'rsa-pss':\n                return determineRsaAlgorithm(privateKey, privateKeyInput, this.issuer.dpop_signing_alg_values_supported);\n            default:\n                throw new TypeError('unsupported DPoP private key');\n        }\n    };\n    const RSPS = /^(?:RS|PS)(?:256|384|512)$/;\n    function determineRsaAlgorithm(privateKey, privateKeyInput, valuesSupported) {\n        if (typeof privateKeyInput === 'object' && privateKeyInput.format === 'jwk' && privateKeyInput.key && privateKeyInput.key.alg) {\n            return privateKeyInput.key.alg;\n        }\n        if (Array.isArray(valuesSupported)) {\n            let candidates = valuesSupported.filter(RegExp.prototype.test.bind(RSPS));\n            if (privateKey.asymmetricKeyType === 'rsa-pss') {\n                candidates = candidates.filter((value)=>value.startsWith('PS'));\n            }\n            return [\n                'PS256',\n                'PS384',\n                'PS512',\n                'RS256',\n                'RS384',\n                'RS384'\n            ].find((preferred)=>candidates.includes(preferred));\n        }\n        return 'PS256';\n    }\n    const p256 = Buffer.from([\n        42,\n        134,\n        72,\n        206,\n        61,\n        3,\n        1,\n        7\n    ]);\n    const p384 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        34\n    ]);\n    const p521 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        35\n    ]);\n    const secp256k1 = Buffer.from([\n        43,\n        129,\n        4,\n        0,\n        10\n    ]);\n    function determineEcAlgorithm(privateKey, privateKeyInput) {\n        // If input was a JWK\n        switch(typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.key.crv){\n            case 'P-256':\n                return 'ES256';\n            case 'secp256k1':\n                return 'ES256K';\n            case 'P-384':\n                return 'ES384';\n            case 'P-512':\n                return 'ES512';\n            default:\n                break;\n        }\n        const buf = privateKey.export({\n            format: 'der',\n            type: 'pkcs8'\n        });\n        const i = buf[1] < 128 ? 17 : 18;\n        const len = buf[i];\n        const curveOid = buf.slice(i + 1, i + 1 + len);\n        if (curveOid.equals(p256)) {\n            return 'ES256';\n        }\n        if (curveOid.equals(p384)) {\n            return 'ES384';\n        }\n        if (curveOid.equals(p521)) {\n            return 'ES512';\n        }\n        if (curveOid.equals(secp256k1)) {\n            return 'ES256K';\n        }\n        throw new TypeError('unsupported DPoP private key curve');\n    }\n} else {\n    determineDPoPAlgorithm = determineDPoPAlgorithmFromCryptoKey;\n}\nconst jwkCache = new WeakMap();\nasync function getJwk(keyObject, privateKeyInput) {\n    if (jose.cryptoRuntime === 'node:crypto' && typeof privateKeyInput === 'object' && typeof privateKeyInput.key === 'object' && privateKeyInput.format === 'jwk') {\n        return pick(privateKeyInput.key, 'kty', 'crv', 'x', 'y', 'e', 'n');\n    }\n    if (jwkCache.has(privateKeyInput)) {\n        return jwkCache.get(privateKeyInput);\n    }\n    const jwk = pick(await jose.exportJWK(keyObject), 'kty', 'crv', 'x', 'y', 'e', 'n');\n    if (isKeyObject(privateKeyInput) || jose.cryptoRuntime === 'WebCryptoAPI') {\n        jwkCache.set(privateKeyInput, jwk);\n    }\n    return jwk;\n}\nmodule.exports = (issuer, aadIssValidation = false)=>class Client extends BaseClient {\n        constructor(...args){\n            super(issuer, aadIssValidation, ...args);\n        }\n        static get issuer() {\n            return issuer;\n        }\n    };\nmodule.exports.BaseClient = BaseClient;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/device_flow_handle.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/device_flow_handle.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\n\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass DeviceFlowHandle {\n  #aborted;\n  #client;\n  #clientAssertionPayload;\n  #DPoP;\n  #exchangeBody;\n  #expires_at;\n  #interval;\n  #maxAge;\n  #response;\n  constructor({ client, exchangeBody, clientAssertionPayload, response, maxAge, DPoP }) {\n    ['verification_uri', 'user_code', 'device_code'].forEach((prop) => {\n      if (typeof response[prop] !== 'string' || !response[prop]) {\n        throw new RPError(\n          `expected ${prop} string to be returned by Device Authorization Response, got %j`,\n          response[prop],\n        );\n      }\n    });\n\n    if (!Number.isSafeInteger(response.expires_in)) {\n      throw new RPError(\n        'expected expires_in number to be returned by Device Authorization Response, got %j',\n        response.expires_in,\n      );\n    }\n\n    this.#expires_at = now() + response.expires_in;\n    this.#client = client;\n    this.#DPoP = DPoP;\n    this.#maxAge = maxAge;\n    this.#exchangeBody = exchangeBody;\n    this.#clientAssertionPayload = clientAssertionPayload;\n    this.#response = response;\n    this.#interval = response.interval * 1000 || 5000;\n  }\n\n  abort() {\n    this.#aborted = true;\n  }\n\n  async poll({ signal } = {}) {\n    if ((signal && signal.aborted) || this.#aborted) {\n      throw new RPError('polling aborted');\n    }\n\n    if (this.expired()) {\n      throw new RPError(\n        'the device code %j has expired and the device authorization session has concluded',\n        this.device_code,\n      );\n    }\n\n    await new Promise((resolve) => setTimeout(resolve, this.#interval));\n\n    let tokenset;\n    try {\n      tokenset = await this.#client.grant(\n        {\n          ...this.#exchangeBody,\n          grant_type: 'urn:ietf:params:oauth:grant-type:device_code',\n          device_code: this.device_code,\n        },\n        { clientAssertionPayload: this.#clientAssertionPayload, DPoP: this.#DPoP },\n      );\n    } catch (err) {\n      switch (err instanceof OPError && err.error) {\n        case 'slow_down':\n          this.#interval += 5000;\n        case 'authorization_pending':\n          return this.poll({ signal });\n        default:\n          throw err;\n      }\n    }\n\n    if ('id_token' in tokenset) {\n      await this.#client.decryptIdToken(tokenset);\n      await this.#client.validateIdToken(tokenset, undefined, 'token', this.#maxAge);\n    }\n\n    return tokenset;\n  }\n\n  get device_code() {\n    return this.#response.device_code;\n  }\n\n  get user_code() {\n    return this.#response.user_code;\n  }\n\n  get verification_uri() {\n    return this.#response.verification_uri;\n  }\n\n  get verification_uri_complete() {\n    return this.#response.verification_uri_complete;\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.#expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.#response, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nmodule.exports = DeviceFlowHandle;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/device_flow_handle.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/errors.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/errors.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { format } = __webpack_require__(/*! util */ \"util\");\n\nclass OPError extends Error {\n  constructor({ error_description, error, error_uri, session_state, state, scope }, response) {\n    super(!error_description ? error : `${error} (${error_description})`);\n\n    Object.assign(\n      this,\n      { error },\n      error_description && { error_description },\n      error_uri && { error_uri },\n      state && { state },\n      scope && { scope },\n      session_state && { session_state },\n    );\n\n    if (response) {\n      Object.defineProperty(this, 'response', {\n        value: response,\n      });\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nclass RPError extends Error {\n  constructor(...args) {\n    if (typeof args[0] === 'string') {\n      super(format(...args));\n    } else {\n      const { message, printf, response, ...rest } = args[0];\n      if (printf) {\n        super(format(...printf));\n      } else {\n        super(message);\n      }\n      Object.assign(this, rest);\n      if (response) {\n        Object.defineProperty(this, 'response', {\n          value: response,\n        });\n      }\n    }\n\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n  }\n}\n\nmodule.exports = {\n  OPError,\n  RPError,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvZXJyb3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsU0FBUyxFQUFFLG1CQUFPLENBQUMsa0JBQU07O0FBRWpDO0FBQ0EsZ0JBQWdCLGtFQUFrRTtBQUNsRiwwQ0FBMEMsT0FBTyxHQUFHLGtCQUFrQjs7QUFFdEU7QUFDQTtBQUNBLFFBQVEsT0FBTztBQUNmLDZCQUE2QixtQkFBbUI7QUFDaEQscUJBQXFCLFdBQVc7QUFDaEMsaUJBQWlCLE9BQU87QUFDeEIsaUJBQWlCLE9BQU87QUFDeEIseUJBQXlCLGVBQWU7QUFDeEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLGNBQWMscUNBQXFDO0FBQ25EO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxlcnJvcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgeyBmb3JtYXQgfSA9IHJlcXVpcmUoJ3V0aWwnKTtcblxuY2xhc3MgT1BFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29uc3RydWN0b3IoeyBlcnJvcl9kZXNjcmlwdGlvbiwgZXJyb3IsIGVycm9yX3VyaSwgc2Vzc2lvbl9zdGF0ZSwgc3RhdGUsIHNjb3BlIH0sIHJlc3BvbnNlKSB7XG4gICAgc3VwZXIoIWVycm9yX2Rlc2NyaXB0aW9uID8gZXJyb3IgOiBgJHtlcnJvcn0gKCR7ZXJyb3JfZGVzY3JpcHRpb259KWApO1xuXG4gICAgT2JqZWN0LmFzc2lnbihcbiAgICAgIHRoaXMsXG4gICAgICB7IGVycm9yIH0sXG4gICAgICBlcnJvcl9kZXNjcmlwdGlvbiAmJiB7IGVycm9yX2Rlc2NyaXB0aW9uIH0sXG4gICAgICBlcnJvcl91cmkgJiYgeyBlcnJvcl91cmkgfSxcbiAgICAgIHN0YXRlICYmIHsgc3RhdGUgfSxcbiAgICAgIHNjb3BlICYmIHsgc2NvcGUgfSxcbiAgICAgIHNlc3Npb25fc3RhdGUgJiYgeyBzZXNzaW9uX3N0YXRlIH0sXG4gICAgKTtcblxuICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsICdyZXNwb25zZScsIHtcbiAgICAgICAgdmFsdWU6IHJlc3BvbnNlLFxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbmNsYXNzIFJQRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKC4uLmFyZ3MpIHtcbiAgICBpZiAodHlwZW9mIGFyZ3NbMF0gPT09ICdzdHJpbmcnKSB7XG4gICAgICBzdXBlcihmb3JtYXQoLi4uYXJncykpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCB7IG1lc3NhZ2UsIHByaW50ZiwgcmVzcG9uc2UsIC4uLnJlc3QgfSA9IGFyZ3NbMF07XG4gICAgICBpZiAocHJpbnRmKSB7XG4gICAgICAgIHN1cGVyKGZvcm1hdCguLi5wcmludGYpKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgfVxuICAgICAgT2JqZWN0LmFzc2lnbih0aGlzLCByZXN0KTtcbiAgICAgIGlmIChyZXNwb25zZSkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGhpcywgJ3Jlc3BvbnNlJywge1xuICAgICAgICAgIHZhbHVlOiByZXNwb25zZSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgdGhpcy5uYW1lID0gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lO1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBPUEVycm9yLFxuICBSUEVycm9yLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/assert.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/assert.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("function assertSigningAlgValuesSupport(endpoint, issuer, properties) {\n  if (!issuer[`${endpoint}_endpoint`]) return;\n\n  const eam = `${endpoint}_endpoint_auth_method`;\n  const easa = `${endpoint}_endpoint_auth_signing_alg`;\n  const easavs = `${endpoint}_endpoint_auth_signing_alg_values_supported`;\n\n  if (properties[eam] && properties[eam].endsWith('_jwt') && !properties[easa] && !issuer[easavs]) {\n    throw new TypeError(\n      `${easavs} must be configured on the issuer if ${easa} is not defined on a client`,\n    );\n  }\n}\n\nfunction assertIssuerConfiguration(issuer, endpoint) {\n  if (!issuer[endpoint]) {\n    throw new TypeError(`${endpoint} must be configured on the issuer`);\n  }\n}\n\nmodule.exports = {\n  assertSigningAlgValuesSupport,\n  assertIssuerConfiguration,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxpQkFBaUIsU0FBUzs7QUFFMUIsaUJBQWlCLFNBQVM7QUFDMUIsa0JBQWtCLFNBQVM7QUFDM0Isb0JBQW9CLFNBQVM7O0FBRTdCO0FBQ0E7QUFDQSxTQUFTLFFBQVEsc0NBQXNDLE1BQU07QUFDN0Q7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSwyQkFBMkIsVUFBVTtBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcYXNzZXJ0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGFzc2VydFNpZ25pbmdBbGdWYWx1ZXNTdXBwb3J0KGVuZHBvaW50LCBpc3N1ZXIsIHByb3BlcnRpZXMpIHtcbiAgaWYgKCFpc3N1ZXJbYCR7ZW5kcG9pbnR9X2VuZHBvaW50YF0pIHJldHVybjtcblxuICBjb25zdCBlYW0gPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9tZXRob2RgO1xuICBjb25zdCBlYXNhID0gYCR7ZW5kcG9pbnR9X2VuZHBvaW50X2F1dGhfc2lnbmluZ19hbGdgO1xuICBjb25zdCBlYXNhdnMgPSBgJHtlbmRwb2ludH1fZW5kcG9pbnRfYXV0aF9zaWduaW5nX2FsZ192YWx1ZXNfc3VwcG9ydGVkYDtcblxuICBpZiAocHJvcGVydGllc1tlYW1dICYmIHByb3BlcnRpZXNbZWFtXS5lbmRzV2l0aCgnX2p3dCcpICYmICFwcm9wZXJ0aWVzW2Vhc2FdICYmICFpc3N1ZXJbZWFzYXZzXSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXG4gICAgICBgJHtlYXNhdnN9IG11c3QgYmUgY29uZmlndXJlZCBvbiB0aGUgaXNzdWVyIGlmICR7ZWFzYX0gaXMgbm90IGRlZmluZWQgb24gYSBjbGllbnRgLFxuICAgICk7XG4gIH1cbn1cblxuZnVuY3Rpb24gYXNzZXJ0SXNzdWVyQ29uZmlndXJhdGlvbihpc3N1ZXIsIGVuZHBvaW50KSB7XG4gIGlmICghaXNzdWVyW2VuZHBvaW50XSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYCR7ZW5kcG9pbnR9IG11c3QgYmUgY29uZmlndXJlZCBvbiB0aGUgaXNzdWVyYCk7XG4gIH1cbn1cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIGFzc2VydFNpZ25pbmdBbGdWYWx1ZXNTdXBwb3J0LFxuICBhc3NlcnRJc3N1ZXJDb25maWd1cmF0aW9uLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/base64url.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/base64url.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("let encode;\nif (Buffer.isEncoding('base64url')) {\n  encode = (input, encoding = 'utf8') => Buffer.from(input, encoding).toString('base64url');\n} else {\n  const fromBase64 = (base64) => base64.replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n  encode = (input, encoding = 'utf8') =>\n    fromBase64(Buffer.from(input, encoding).toString('base64'));\n}\n\nconst decode = (input) => Buffer.from(input, 'base64');\n\nmodule.exports.decode = decode;\nmodule.exports.encode = encode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLHFCQUFxQjtBQUNyQixxQkFBcUIiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxiYXNlNjR1cmwuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVuY29kZTtcbmlmIChCdWZmZXIuaXNFbmNvZGluZygnYmFzZTY0dXJsJykpIHtcbiAgZW5jb2RlID0gKGlucHV0LCBlbmNvZGluZyA9ICd1dGY4JykgPT4gQnVmZmVyLmZyb20oaW5wdXQsIGVuY29kaW5nKS50b1N0cmluZygnYmFzZTY0dXJsJyk7XG59IGVsc2Uge1xuICBjb25zdCBmcm9tQmFzZTY0ID0gKGJhc2U2NCkgPT4gYmFzZTY0LnJlcGxhY2UoLz0vZywgJycpLnJlcGxhY2UoL1xcKy9nLCAnLScpLnJlcGxhY2UoL1xcLy9nLCAnXycpO1xuICBlbmNvZGUgPSAoaW5wdXQsIGVuY29kaW5nID0gJ3V0ZjgnKSA9PlxuICAgIGZyb21CYXNlNjQoQnVmZmVyLmZyb20oaW5wdXQsIGVuY29kaW5nKS50b1N0cmluZygnYmFzZTY0JykpO1xufVxuXG5jb25zdCBkZWNvZGUgPSAoaW5wdXQpID0+IEJ1ZmZlci5mcm9tKGlucHV0LCAnYmFzZTY0Jyk7XG5cbm1vZHVsZS5leHBvcnRzLmRlY29kZSA9IGRlY29kZTtcbm1vZHVsZS5leHBvcnRzLmVuY29kZSA9IGVuY29kZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/client.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/client.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst { random } = __webpack_require__(/*! ./generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst now = __webpack_require__(/*! ./unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst merge = __webpack_require__(/*! ./merge */ \"(rsc)/./node_modules/openid-client/lib/helpers/merge.js\");\n\n// TODO: in v6.x additionally encode the `- _ . ! ~ * ' ( )` characters\n// https://github.com/panva/node-openid-client/commit/5a2ea80ef5e59ec0c03dbd97d82f551e24a9d348\nconst formUrlEncode = (value) => encodeURIComponent(value).replace(/%20/g, '+');\n\nasync function clientAssertion(endpoint, payload) {\n  let alg = this[`${endpoint}_endpoint_auth_signing_alg`];\n  if (!alg) {\n    assertIssuerConfiguration(\n      this.issuer,\n      `${endpoint}_endpoint_auth_signing_alg_values_supported`,\n    );\n  }\n\n  if (this[`${endpoint}_endpoint_auth_method`] === 'client_secret_jwt') {\n    if (!alg) {\n      const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n      alg =\n        Array.isArray(supported) && supported.find((signAlg) => /^HS(?:256|384|512)/.test(signAlg));\n    }\n\n    if (!alg) {\n      throw new RPError(\n        `failed to determine a JWS Algorithm to use for ${\n          this[`${endpoint}_endpoint_auth_method`]\n        } Client Assertion`,\n      );\n    }\n\n    return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n      .setProtectedHeader({ alg })\n      .sign(this.secretForAlg(alg));\n  }\n\n  const keystore = await keystores.get(this);\n\n  if (!keystore) {\n    throw new TypeError('no client jwks provided for signing a client assertion with');\n  }\n\n  if (!alg) {\n    const supported = this.issuer[`${endpoint}_endpoint_auth_signing_alg_values_supported`];\n    alg =\n      Array.isArray(supported) &&\n      supported.find((signAlg) => keystore.get({ alg: signAlg, use: 'sig' }));\n  }\n\n  if (!alg) {\n    throw new RPError(\n      `failed to determine a JWS Algorithm to use for ${\n        this[`${endpoint}_endpoint_auth_method`]\n      } Client Assertion`,\n    );\n  }\n\n  const key = keystore.get({ alg, use: 'sig' });\n  if (!key) {\n    throw new RPError(\n      `no key found in client jwks to sign a client assertion with using alg ${alg}`,\n    );\n  }\n\n  return new jose.CompactSign(Buffer.from(JSON.stringify(payload)))\n    .setProtectedHeader({ alg, kid: key.jwk && key.jwk.kid })\n    .sign(await key.keyObject(alg));\n}\n\nasync function authFor(endpoint, { clientAssertionPayload } = {}) {\n  const authMethod = this[`${endpoint}_endpoint_auth_method`];\n  switch (authMethod) {\n    case 'self_signed_tls_client_auth':\n    case 'tls_client_auth':\n    case 'none':\n      return { form: { client_id: this.client_id } };\n    case 'client_secret_post':\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_post client authentication method requires a client_secret',\n        );\n      }\n      return { form: { client_id: this.client_id, client_secret: this.client_secret } };\n    case 'private_key_jwt':\n    case 'client_secret_jwt': {\n      const timestamp = now();\n      const audience = [\n        ...new Set([this.issuer.issuer, this.issuer.token_endpoint].filter(Boolean)),\n      ];\n\n      const assertion = await clientAssertion.call(this, endpoint, {\n        iat: timestamp,\n        exp: timestamp + 60,\n        jti: random(),\n        iss: this.client_id,\n        sub: this.client_id,\n        aud: audience,\n        ...clientAssertionPayload,\n      });\n\n      return {\n        form: {\n          client_id: this.client_id,\n          client_assertion: assertion,\n          client_assertion_type: 'urn:ietf:params:oauth:client-assertion-type:jwt-bearer',\n        },\n      };\n    }\n    case 'client_secret_basic': {\n      // This is correct behaviour, see https://tools.ietf.org/html/rfc6749#section-2.3.1 and the\n      // related appendix. (also https://github.com/panva/node-openid-client/pull/91)\n      // > The client identifier is encoded using the\n      // > \"application/x-www-form-urlencoded\" encoding algorithm per\n      // > Appendix B, and the encoded value is used as the username; the client\n      // > password is encoded using the same algorithm and used as the\n      // > password.\n      if (typeof this.client_secret !== 'string') {\n        throw new TypeError(\n          'client_secret_basic client authentication method requires a client_secret',\n        );\n      }\n      const encoded = `${formUrlEncode(this.client_id)}:${formUrlEncode(this.client_secret)}`;\n      const value = Buffer.from(encoded).toString('base64');\n      return { headers: { Authorization: `Basic ${value}` } };\n    }\n    default: {\n      throw new TypeError(`missing, or unsupported, ${endpoint}_endpoint_auth_method`);\n    }\n  }\n}\n\nfunction resolveResponseType() {\n  const { length, 0: value } = this.response_types;\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nfunction resolveRedirectUri() {\n  const { length, 0: value } = this.redirect_uris || [];\n\n  if (length === 1) {\n    return value;\n  }\n\n  return undefined;\n}\n\nasync function authenticatedPost(\n  endpoint,\n  opts,\n  { clientAssertionPayload, endpointAuthMethod = endpoint, DPoP } = {},\n) {\n  const auth = await authFor.call(this, endpointAuthMethod, { clientAssertionPayload });\n  const requestOpts = merge(opts, auth);\n\n  const mTLS =\n    this[`${endpointAuthMethod}_endpoint_auth_method`].includes('tls_client_auth') ||\n    (endpoint === 'token' && this.tls_client_certificate_bound_access_tokens);\n\n  let targetUrl;\n  if (mTLS && this.issuer.mtls_endpoint_aliases) {\n    targetUrl = this.issuer.mtls_endpoint_aliases[`${endpoint}_endpoint`];\n  }\n\n  targetUrl = targetUrl || this.issuer[`${endpoint}_endpoint`];\n\n  if ('form' in requestOpts) {\n    for (const [key, value] of Object.entries(requestOpts.form)) {\n      if (typeof value === 'undefined') {\n        delete requestOpts.form[key];\n      }\n    }\n  }\n\n  return request.call(\n    this,\n    {\n      ...requestOpts,\n      method: 'POST',\n      url: targetUrl,\n      headers: {\n        ...(endpoint !== 'revocation'\n          ? {\n              Accept: 'application/json',\n            }\n          : undefined),\n        ...requestOpts.headers,\n      },\n    },\n    { mTLS, DPoP },\n  );\n}\n\nmodule.exports = {\n  resolveResponseType,\n  resolveRedirectUri,\n  authFor,\n  authenticatedPost,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/consts.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/consts.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("const HTTP_OPTIONS = Symbol();\nconst CLOCK_TOLERANCE = Symbol();\n\nmodule.exports = {\n  CLOCK_TOLERANCE,\n  HTTP_OPTIONS,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9jb25zdHMuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGNvbnN0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBIVFRQX09QVElPTlMgPSBTeW1ib2woKTtcbmNvbnN0IENMT0NLX1RPTEVSQU5DRSA9IFN5bWJvbCgpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQ0xPQ0tfVE9MRVJBTkNFLFxuICBIVFRQX09QVElPTlMsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/consts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/decode_jwt.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nmodule.exports = (token) => {\n  if (typeof token !== 'string' || !token) {\n    throw new TypeError('JWT must be a string');\n  }\n\n  const { 0: header, 1: payload, 2: signature, length } = token.split('.');\n\n  if (length === 5) {\n    throw new TypeError('encrypted JWTs cannot be decoded');\n  }\n\n  if (length !== 3) {\n    throw new Error('JWTs must have three components');\n  }\n\n  try {\n    return {\n      header: JSON.parse(base64url.decode(header)),\n      payload: JSON.parse(base64url.decode(payload)),\n      signature,\n    };\n  } catch (err) {\n    throw new Error('JWT is malformed');\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWNvZGVfand0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLGdGQUFhOztBQUV2QztBQUNBO0FBQ0E7QUFDQTs7QUFFQSxVQUFVLDhDQUE4Qzs7QUFFeEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZGVjb2RlX2p3dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2Jhc2U2NHVybCcpO1xuXG5tb2R1bGUuZXhwb3J0cyA9ICh0b2tlbikgPT4ge1xuICBpZiAodHlwZW9mIHRva2VuICE9PSAnc3RyaW5nJyB8fCAhdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdKV1QgbXVzdCBiZSBhIHN0cmluZycpO1xuICB9XG5cbiAgY29uc3QgeyAwOiBoZWFkZXIsIDE6IHBheWxvYWQsIDI6IHNpZ25hdHVyZSwgbGVuZ3RoIH0gPSB0b2tlbi5zcGxpdCgnLicpO1xuXG4gIGlmIChsZW5ndGggPT09IDUpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdlbmNyeXB0ZWQgSldUcyBjYW5ub3QgYmUgZGVjb2RlZCcpO1xuICB9XG5cbiAgaWYgKGxlbmd0aCAhPT0gMykge1xuICAgIHRocm93IG5ldyBFcnJvcignSldUcyBtdXN0IGhhdmUgdGhyZWUgY29tcG9uZW50cycpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICByZXR1cm4ge1xuICAgICAgaGVhZGVyOiBKU09OLnBhcnNlKGJhc2U2NHVybC5kZWNvZGUoaGVhZGVyKSksXG4gICAgICBwYXlsb2FkOiBKU09OLnBhcnNlKGJhc2U2NHVybC5kZWNvZGUocGF5bG9hZCkpLFxuICAgICAgc2lnbmF0dXJlLFxuICAgIH07XG4gIH0gY2F0Y2ggKGVycikge1xuICAgIHRocm93IG5ldyBFcnJvcignSldUIGlzIG1hbGZvcm1lZCcpO1xuICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/decode_jwt.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/deep_clone.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports = globalThis.structuredClone || ((obj) => JSON.parse(JSON.stringify(obj)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWVwX2Nsb25lLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcZGVlcF9jbG9uZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGdsb2JhbFRoaXMuc3RydWN0dXJlZENsb25lIHx8ICgob2JqKSA9PiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KG9iaikpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/defaults.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/defaults.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction defaults(deep, target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (typeof target[key] === 'undefined' && typeof value !== 'undefined') {\n        target[key] = value;\n      }\n\n      if (deep && isPlainObject(target[key]) && isPlainObject(value)) {\n        defaults(true, target[key], value);\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = defaults.bind(undefined, false);\nmodule.exports.deep = defaults.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9kZWZhdWx0cy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLG1CQUFtQiIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXGRlZmF1bHRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCcuL2lzX3BsYWluX29iamVjdCcpO1xuXG5mdW5jdGlvbiBkZWZhdWx0cyhkZWVwLCB0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgZm9yIChjb25zdCBzb3VyY2Ugb2Ygc291cmNlcykge1xuICAgIGlmICghaXNQbGFpbk9iamVjdChzb3VyY2UpKSB7XG4gICAgICBjb250aW51ZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgLyogaXN0YW5idWwgaWdub3JlIGlmICovXG4gICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicpIHtcbiAgICAgICAgY29udGludWU7XG4gICAgICB9XG4gICAgICBpZiAodHlwZW9mIHRhcmdldFtrZXldID09PSAndW5kZWZpbmVkJyAmJiB0eXBlb2YgdmFsdWUgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIHRhcmdldFtrZXldID0gdmFsdWU7XG4gICAgICB9XG5cbiAgICAgIGlmIChkZWVwICYmIGlzUGxhaW5PYmplY3QodGFyZ2V0W2tleV0pICYmIGlzUGxhaW5PYmplY3QodmFsdWUpKSB7XG4gICAgICAgIGRlZmF1bHRzKHRydWUsIHRhcmdldFtrZXldLCB2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHRhcmdldDtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBkZWZhdWx0cy5iaW5kKHVuZGVmaW5lZCwgZmFsc2UpO1xubW9kdWxlLmV4cG9ydHMuZGVlcCA9IGRlZmF1bHRzLmJpbmQodW5kZWZpbmVkLCB0cnVlKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/generators.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/generators.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { createHash, randomBytes } = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst base64url = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\n\nconst random = (bytes = 32) => base64url.encode(randomBytes(bytes));\n\nmodule.exports = {\n  random,\n  state: random,\n  nonce: random,\n  codeVerifier: random,\n  codeChallenge: (codeVerifier) =>\n    base64url.encode(createHash('sha256').update(codeVerifier).digest()),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9nZW5lcmF0b3JzLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsMEJBQTBCLEVBQUUsbUJBQU8sQ0FBQyxzQkFBUTs7QUFFcEQsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQWE7O0FBRXZDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxnZW5lcmF0b3JzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHsgY3JlYXRlSGFzaCwgcmFuZG9tQnl0ZXMgfSA9IHJlcXVpcmUoJ2NyeXB0bycpO1xuXG5jb25zdCBiYXNlNjR1cmwgPSByZXF1aXJlKCcuL2Jhc2U2NHVybCcpO1xuXG5jb25zdCByYW5kb20gPSAoYnl0ZXMgPSAzMikgPT4gYmFzZTY0dXJsLmVuY29kZShyYW5kb21CeXRlcyhieXRlcykpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgcmFuZG9tLFxuICBzdGF0ZTogcmFuZG9tLFxuICBub25jZTogcmFuZG9tLFxuICBjb2RlVmVyaWZpZXI6IHJhbmRvbSxcbiAgY29kZUNoYWxsZW5nZTogKGNvZGVWZXJpZmllcikgPT5cbiAgICBiYXNlNjR1cmwuZW5jb2RlKGNyZWF0ZUhhc2goJ3NoYTI1NicpLnVwZGF0ZShjb2RlVmVyaWZpZXIpLmRpZ2VzdCgpKSxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/generators.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js":
/*!*****************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_key_object.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const util = __webpack_require__(/*! util */ \"util\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\nmodule.exports = util.types.isKeyObject || ((obj) => obj && obj instanceof crypto.KeyObject);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixlQUFlLG1CQUFPLENBQUMsc0JBQVE7O0FBRS9CIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcaXNfa2V5X29iamVjdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB1dGlsID0gcmVxdWlyZSgndXRpbCcpO1xuY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gdXRpbC50eXBlcy5pc0tleU9iamVjdCB8fCAoKG9iaikgPT4gb2JqICYmIG9iaiBpbnN0YW5jZW9mIGNyeXB0by5LZXlPYmplY3QpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js":
/*!*******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/is_plain_object.js ***!
  \*******************************************************************/
/***/ ((module) => {

eval("module.exports = (a) => !!a && a.constructor === Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9pc19wbGFpbl9vYmplY3QuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxoZWxwZXJzXFxpc19wbGFpbl9vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSAoYSkgPT4gISFhICYmIGEuY29uc3RydWN0b3IgPT09IE9iamVjdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/issuer.js":
/*!**********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/issuer.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const objectHash = __webpack_require__(/*! object-hash */ \"(rsc)/./node_modules/openid-client/node_modules/object-hash/index.js\");\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst { assertIssuerConfiguration } = __webpack_require__(/*! ./assert */ \"(rsc)/./node_modules/openid-client/lib/helpers/assert.js\");\nconst KeyStore = __webpack_require__(/*! ./keystore */ \"(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\");\nconst { keystores } = __webpack_require__(/*! ./weak_cache */ \"(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\");\nconst processResponse = __webpack_require__(/*! ./process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst request = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nconst inFlight = new WeakMap();\nconst caches = new WeakMap();\nconst lrus = (ctx) => {\n  if (!caches.has(ctx)) {\n    caches.set(ctx, new LRU({ max: 100 }));\n  }\n  return caches.get(ctx);\n};\n\nasync function getKeyStore(reload = false) {\n  assertIssuerConfiguration(this, 'jwks_uri');\n\n  const keystore = keystores.get(this);\n  const cache = lrus(this);\n\n  if (reload || !keystore) {\n    if (inFlight.has(this)) {\n      return inFlight.get(this);\n    }\n    cache.reset();\n    inFlight.set(\n      this,\n      (async () => {\n        const response = await request\n          .call(this, {\n            method: 'GET',\n            responseType: 'json',\n            url: this.jwks_uri,\n            headers: {\n              Accept: 'application/json, application/jwk-set+json',\n            },\n          })\n          .finally(() => {\n            inFlight.delete(this);\n          });\n        const jwks = processResponse(response);\n\n        const joseKeyStore = KeyStore.fromJWKS(jwks, { onlyPublic: true });\n        cache.set('throttle', true, 60 * 1000);\n        keystores.set(this, joseKeyStore);\n\n        return joseKeyStore;\n      })(),\n    );\n\n    return inFlight.get(this);\n  }\n\n  return keystore;\n}\n\nasync function queryKeyStore({ kid, kty, alg, use }, { allowMulti = false } = {}) {\n  const cache = lrus(this);\n\n  const def = {\n    kid,\n    kty,\n    alg,\n    use,\n  };\n\n  const defHash = objectHash(def, {\n    algorithm: 'sha256',\n    ignoreUnknown: true,\n    unorderedArrays: true,\n    unorderedSets: true,\n    respectType: false,\n  });\n\n  // refresh keystore on every unknown key but also only upto once every minute\n  const freshJwksUri = cache.get(defHash) || cache.get('throttle');\n\n  const keystore = await getKeyStore.call(this, !freshJwksUri);\n  const keys = keystore.all(def);\n\n  delete def.use;\n  if (keys.length === 0) {\n    throw new RPError({\n      printf: [\"no valid key found in issuer's jwks_uri for key parameters %j\", def],\n      jwks: keystore,\n    });\n  }\n\n  if (!allowMulti && keys.length > 1 && !kid) {\n    throw new RPError({\n      printf: [\n        \"multiple matching keys found in issuer's jwks_uri for key parameters %j, kid must be provided in this case\",\n        def,\n      ],\n      jwks: keystore,\n    });\n  }\n\n  cache.set(defHash, true);\n\n  return keys;\n}\n\nmodule.exports.queryKeyStore = queryKeyStore;\nmodule.exports.keystore = getKeyStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/keystore.js":
/*!************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/keystore.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\n\nconst clone = __webpack_require__(/*! ./deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nconst internal = Symbol();\n\nconst keyscore = (key, { alg, use }) => {\n  let score = 0;\n\n  if (alg && key.alg) {\n    score++;\n  }\n\n  if (use && key.use) {\n    score++;\n  }\n\n  return score;\n};\n\nfunction getKtyFromAlg(alg) {\n  switch (typeof alg === 'string' && alg.slice(0, 2)) {\n    case 'RS':\n    case 'PS':\n      return 'RSA';\n    case 'ES':\n      return 'EC';\n    case 'Ed':\n      return 'OKP';\n    default:\n      return undefined;\n  }\n}\n\nfunction getAlgorithms(use, alg, kty, crv) {\n  // Ed25519, Ed448, and secp256k1 always have \"alg\"\n  // OKP always has \"use\"\n  if (alg) {\n    return new Set([alg]);\n  }\n\n  switch (kty) {\n    case 'EC': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n      }\n\n      if (use === 'sig' || use === undefined) {\n        switch (crv) {\n          case 'P-256':\n          case 'P-384':\n            algs = algs.concat([`ES${crv.slice(-3)}`]);\n            break;\n          case 'P-521':\n            algs = algs.concat(['ES512']);\n            break;\n          case 'secp256k1':\n            if (jose.cryptoRuntime === 'node:crypto') {\n              algs = algs.concat(['ES256K']);\n            }\n            break;\n        }\n      }\n\n      return new Set(algs);\n    }\n    case 'OKP': {\n      return new Set(['ECDH-ES', 'ECDH-ES+A128KW', 'ECDH-ES+A192KW', 'ECDH-ES+A256KW']);\n    }\n    case 'RSA': {\n      let algs = [];\n\n      if (use === 'enc' || use === undefined) {\n        algs = algs.concat(['RSA-OAEP', 'RSA-OAEP-256', 'RSA-OAEP-384', 'RSA-OAEP-512']);\n        if (jose.cryptoRuntime === 'node:crypto') {\n          algs = algs.concat(['RSA1_5']);\n        }\n      }\n\n      if (use === 'sig' || use === undefined) {\n        algs = algs.concat(['PS256', 'PS384', 'PS512', 'RS256', 'RS384', 'RS512']);\n      }\n\n      return new Set(algs);\n    }\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nmodule.exports = class KeyStore {\n  #keys;\n\n  constructor(i, keys) {\n    if (i !== internal) throw new Error('invalid constructor call');\n    this.#keys = keys;\n  }\n\n  toJWKS() {\n    return {\n      keys: this.map(({ jwk: { d, p, q, dp, dq, qi, ...jwk } }) => jwk),\n    };\n  }\n\n  all({ alg, kid, use } = {}) {\n    if (!use || !alg) {\n      throw new Error();\n    }\n\n    const kty = getKtyFromAlg(alg);\n\n    const search = { alg, use };\n    return this.filter((key) => {\n      let candidate = true;\n\n      if (candidate && kty !== undefined && key.jwk.kty !== kty) {\n        candidate = false;\n      }\n\n      if (candidate && kid !== undefined && key.jwk.kid !== kid) {\n        candidate = false;\n      }\n\n      if (candidate && use !== undefined && key.jwk.use !== undefined && key.jwk.use !== use) {\n        candidate = false;\n      }\n\n      if (candidate && key.jwk.alg && key.jwk.alg !== alg) {\n        candidate = false;\n      } else if (!key.algorithms.has(alg)) {\n        candidate = false;\n      }\n\n      return candidate;\n    }).sort((first, second) => keyscore(second, search) - keyscore(first, search));\n  }\n\n  get(...args) {\n    return this.all(...args)[0];\n  }\n\n  static async fromJWKS(jwks, { onlyPublic = false, onlyPrivate = false } = {}) {\n    if (\n      !isPlainObject(jwks) ||\n      !Array.isArray(jwks.keys) ||\n      jwks.keys.some((k) => !isPlainObject(k) || !('kty' in k))\n    ) {\n      throw new TypeError('jwks must be a JSON Web Key Set formatted object');\n    }\n\n    const keys = [];\n\n    for (let jwk of jwks.keys) {\n      jwk = clone(jwk);\n      const { kty, kid, crv } = jwk;\n\n      let { alg, use } = jwk;\n\n      if (typeof kty !== 'string' || !kty) {\n        continue;\n      }\n\n      if (use !== undefined && use !== 'sig' && use !== 'enc') {\n        continue;\n      }\n\n      if (typeof alg !== 'string' && alg !== undefined) {\n        continue;\n      }\n\n      if (typeof kid !== 'string' && kid !== undefined) {\n        continue;\n      }\n\n      if (kty === 'EC' && use === 'sig') {\n        switch (crv) {\n          case 'P-256':\n            alg = 'ES256';\n            break;\n          case 'P-384':\n            alg = 'ES384';\n            break;\n          case 'P-521':\n            alg = 'ES512';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (crv === 'secp256k1') {\n        use = 'sig';\n        alg = 'ES256K';\n      }\n\n      if (kty === 'OKP') {\n        switch (crv) {\n          case 'Ed25519':\n          case 'Ed448':\n            use = 'sig';\n            alg = 'EdDSA';\n            break;\n          case 'X25519':\n          case 'X448':\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (alg && !use) {\n        switch (true) {\n          case alg.startsWith('ECDH'):\n            use = 'enc';\n            break;\n          case alg.startsWith('RSA'):\n            use = 'enc';\n            break;\n          default:\n            break;\n        }\n      }\n\n      if (onlyPrivate && (jwk.kty === 'oct' || !jwk.d)) {\n        throw new Error('jwks must only contain private keys');\n      }\n\n      if (onlyPublic && (jwk.d || jwk.k)) {\n        continue;\n      }\n\n      keys.push({\n        jwk: { ...jwk, alg, use },\n        async keyObject(alg) {\n          if (this[alg]) {\n            return this[alg];\n          }\n\n          const keyObject = await jose.importJWK(this.jwk, alg);\n          this[alg] = keyObject;\n          return keyObject;\n        },\n        get algorithms() {\n          Object.defineProperty(this, 'algorithms', {\n            value: getAlgorithms(this.jwk.use, this.jwk.alg, this.jwk.kty, this.jwk.crv),\n            enumerable: true,\n            configurable: false,\n          });\n          return this.algorithms;\n        },\n      });\n    }\n\n    return new this(internal, keys);\n  }\n\n  filter(...args) {\n    return this.#keys.filter(...args);\n  }\n\n  find(...args) {\n    return this.#keys.find(...args);\n  }\n\n  every(...args) {\n    return this.#keys.every(...args);\n  }\n\n  some(...args) {\n    return this.#keys.some(...args);\n  }\n\n  map(...args) {\n    return this.#keys.map(...args);\n  }\n\n  forEach(...args) {\n    return this.#keys.forEach(...args);\n  }\n\n  reduce(...args) {\n    return this.#keys.reduce(...args);\n  }\n\n  sort(...args) {\n    return this.#keys.sort(...args);\n  }\n\n  *[Symbol.iterator]() {\n    for (const key of this.#keys) {\n      yield key;\n    }\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/keystore.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/merge.js":
/*!*********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/merge.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const isPlainObject = __webpack_require__(/*! ./is_plain_object */ \"(rsc)/./node_modules/openid-client/lib/helpers/is_plain_object.js\");\n\nfunction merge(target, ...sources) {\n  for (const source of sources) {\n    if (!isPlainObject(source)) {\n      continue;\n    }\n    for (const [key, value] of Object.entries(source)) {\n      /* istanbul ignore if */\n      if (key === '__proto__' || key === 'constructor') {\n        continue;\n      }\n      if (isPlainObject(target[key]) && isPlainObject(value)) {\n        target[key] = merge(target[key], value);\n      } else if (typeof value !== 'undefined') {\n        target[key] = value;\n      }\n    }\n  }\n\n  return target;\n}\n\nmodule.exports = merge;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9tZXJnZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxzQkFBc0IsbUJBQU8sQ0FBQyw0RkFBbUI7O0FBRWpEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNQbGFpbk9iamVjdCA9IHJlcXVpcmUoJy4vaXNfcGxhaW5fb2JqZWN0Jyk7XG5cbmZ1bmN0aW9uIG1lcmdlKHRhcmdldCwgLi4uc291cmNlcykge1xuICBmb3IgKGNvbnN0IHNvdXJjZSBvZiBzb3VyY2VzKSB7XG4gICAgaWYgKCFpc1BsYWluT2JqZWN0KHNvdXJjZSkpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhzb3VyY2UpKSB7XG4gICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgaWYgKi9cbiAgICAgIGlmIChrZXkgPT09ICdfX3Byb3RvX18nIHx8IGtleSA9PT0gJ2NvbnN0cnVjdG9yJykge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGlmIChpc1BsYWluT2JqZWN0KHRhcmdldFtrZXldKSAmJiBpc1BsYWluT2JqZWN0KHZhbHVlKSkge1xuICAgICAgICB0YXJnZXRba2V5XSA9IG1lcmdlKHRhcmdldFtrZXldLCB2YWx1ZSk7XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSB2YWx1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gdGFyZ2V0O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1lcmdlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/pick.js":
/*!********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/pick.js ***!
  \********************************************************/
/***/ ((module) => {

eval("module.exports = function pick(object, ...paths) {\n  const obj = {};\n  for (const path of paths) {\n    if (object[path] !== undefined) {\n      obj[path] = object[path];\n    }\n  }\n  return obj;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9waWNrLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHBpY2suanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBwaWNrKG9iamVjdCwgLi4ucGF0aHMpIHtcbiAgY29uc3Qgb2JqID0ge307XG4gIGZvciAoY29uc3QgcGF0aCBvZiBwYXRocykge1xuICAgIGlmIChvYmplY3RbcGF0aF0gIT09IHVuZGVmaW5lZCkge1xuICAgICAgb2JqW3BhdGhdID0gb2JqZWN0W3BhdGhdO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb2JqO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/pick.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/process_response.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/process_response.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { STATUS_CODES } = __webpack_require__(/*! http */ \"http\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst { OPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst parseWwwAuthenticate = __webpack_require__(/*! ./www_authenticate_parser */ \"(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\");\n\nconst throwAuthenticateErrors = (response) => {\n  const params = parseWwwAuthenticate(response.headers['www-authenticate']);\n\n  if (params.error) {\n    throw new OPError(params, response);\n  }\n};\n\nconst isStandardBodyError = (response) => {\n  let result = false;\n  try {\n    let jsonbody;\n    if (typeof response.body !== 'object' || Buffer.isBuffer(response.body)) {\n      jsonbody = JSON.parse(response.body);\n    } else {\n      jsonbody = response.body;\n    }\n    result = typeof jsonbody.error === 'string' && jsonbody.error.length;\n    if (result) Object.defineProperty(response, 'body', { value: jsonbody, configurable: true });\n  } catch (err) {}\n\n  return result;\n};\n\nfunction processResponse(response, { statusCode = 200, body = true, bearer = false } = {}) {\n  if (response.statusCode !== statusCode) {\n    if (bearer) {\n      throwAuthenticateErrors(response);\n    }\n\n    if (isStandardBodyError(response)) {\n      throw new OPError(response.body, response);\n    }\n\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s, got: %i %s',\n          statusCode,\n          STATUS_CODES[statusCode],\n          response.statusCode,\n          STATUS_CODES[response.statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  if (body && !response.body) {\n    throw new OPError(\n      {\n        error: format(\n          'expected %i %s with body but no body was returned',\n          statusCode,\n          STATUS_CODES[statusCode],\n        ),\n      },\n      response,\n    );\n  }\n\n  return response.body;\n}\n\nmodule.exports = processResponse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy9wcm9jZXNzX3Jlc3BvbnNlLmpzIiwibWFwcGluZ3MiOiJBQUFBLFFBQVEsZUFBZSxFQUFFLG1CQUFPLENBQUMsa0JBQU07QUFDdkMsUUFBUSxTQUFTLEVBQUUsbUJBQU8sQ0FBQyxrQkFBTTs7QUFFakMsUUFBUSxVQUFVLEVBQUUsbUJBQU8sQ0FBQyxtRUFBVztBQUN2Qyw2QkFBNkIsbUJBQU8sQ0FBQyw0R0FBMkI7O0FBRWhFO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLDBEQUEwRCxxQ0FBcUM7QUFDL0YsSUFBSTs7QUFFSjtBQUNBOztBQUVBLHFDQUFxQyxnREFBZ0QsSUFBSTtBQUN6RjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xccHJvY2Vzc19yZXNwb25zZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB7IFNUQVRVU19DT0RFUyB9ID0gcmVxdWlyZSgnaHR0cCcpO1xuY29uc3QgeyBmb3JtYXQgfSA9IHJlcXVpcmUoJ3V0aWwnKTtcblxuY29uc3QgeyBPUEVycm9yIH0gPSByZXF1aXJlKCcuLi9lcnJvcnMnKTtcbmNvbnN0IHBhcnNlV3d3QXV0aGVudGljYXRlID0gcmVxdWlyZSgnLi93d3dfYXV0aGVudGljYXRlX3BhcnNlcicpO1xuXG5jb25zdCB0aHJvd0F1dGhlbnRpY2F0ZUVycm9ycyA9IChyZXNwb25zZSkgPT4ge1xuICBjb25zdCBwYXJhbXMgPSBwYXJzZVd3d0F1dGhlbnRpY2F0ZShyZXNwb25zZS5oZWFkZXJzWyd3d3ctYXV0aGVudGljYXRlJ10pO1xuXG4gIGlmIChwYXJhbXMuZXJyb3IpIHtcbiAgICB0aHJvdyBuZXcgT1BFcnJvcihwYXJhbXMsIHJlc3BvbnNlKTtcbiAgfVxufTtcblxuY29uc3QgaXNTdGFuZGFyZEJvZHlFcnJvciA9IChyZXNwb25zZSkgPT4ge1xuICBsZXQgcmVzdWx0ID0gZmFsc2U7XG4gIHRyeSB7XG4gICAgbGV0IGpzb25ib2R5O1xuICAgIGlmICh0eXBlb2YgcmVzcG9uc2UuYm9keSAhPT0gJ29iamVjdCcgfHwgQnVmZmVyLmlzQnVmZmVyKHJlc3BvbnNlLmJvZHkpKSB7XG4gICAgICBqc29uYm9keSA9IEpTT04ucGFyc2UocmVzcG9uc2UuYm9keSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGpzb25ib2R5ID0gcmVzcG9uc2UuYm9keTtcbiAgICB9XG4gICAgcmVzdWx0ID0gdHlwZW9mIGpzb25ib2R5LmVycm9yID09PSAnc3RyaW5nJyAmJiBqc29uYm9keS5lcnJvci5sZW5ndGg7XG4gICAgaWYgKHJlc3VsdCkgT2JqZWN0LmRlZmluZVByb3BlcnR5KHJlc3BvbnNlLCAnYm9keScsIHsgdmFsdWU6IGpzb25ib2R5LCBjb25maWd1cmFibGU6IHRydWUgfSk7XG4gIH0gY2F0Y2ggKGVycikge31cblxuICByZXR1cm4gcmVzdWx0O1xufTtcblxuZnVuY3Rpb24gcHJvY2Vzc1Jlc3BvbnNlKHJlc3BvbnNlLCB7IHN0YXR1c0NvZGUgPSAyMDAsIGJvZHkgPSB0cnVlLCBiZWFyZXIgPSBmYWxzZSB9ID0ge30pIHtcbiAgaWYgKHJlc3BvbnNlLnN0YXR1c0NvZGUgIT09IHN0YXR1c0NvZGUpIHtcbiAgICBpZiAoYmVhcmVyKSB7XG4gICAgICB0aHJvd0F1dGhlbnRpY2F0ZUVycm9ycyhyZXNwb25zZSk7XG4gICAgfVxuXG4gICAgaWYgKGlzU3RhbmRhcmRCb2R5RXJyb3IocmVzcG9uc2UpKSB7XG4gICAgICB0aHJvdyBuZXcgT1BFcnJvcihyZXNwb25zZS5ib2R5LCByZXNwb25zZSk7XG4gICAgfVxuXG4gICAgdGhyb3cgbmV3IE9QRXJyb3IoXG4gICAgICB7XG4gICAgICAgIGVycm9yOiBmb3JtYXQoXG4gICAgICAgICAgJ2V4cGVjdGVkICVpICVzLCBnb3Q6ICVpICVzJyxcbiAgICAgICAgICBzdGF0dXNDb2RlLFxuICAgICAgICAgIFNUQVRVU19DT0RFU1tzdGF0dXNDb2RlXSxcbiAgICAgICAgICByZXNwb25zZS5zdGF0dXNDb2RlLFxuICAgICAgICAgIFNUQVRVU19DT0RFU1tyZXNwb25zZS5zdGF0dXNDb2RlXSxcbiAgICAgICAgKSxcbiAgICAgIH0sXG4gICAgICByZXNwb25zZSxcbiAgICApO1xuICB9XG5cbiAgaWYgKGJvZHkgJiYgIXJlc3BvbnNlLmJvZHkpIHtcbiAgICB0aHJvdyBuZXcgT1BFcnJvcihcbiAgICAgIHtcbiAgICAgICAgZXJyb3I6IGZvcm1hdChcbiAgICAgICAgICAnZXhwZWN0ZWQgJWkgJXMgd2l0aCBib2R5IGJ1dCBubyBib2R5IHdhcyByZXR1cm5lZCcsXG4gICAgICAgICAgc3RhdHVzQ29kZSxcbiAgICAgICAgICBTVEFUVVNfQ09ERVNbc3RhdHVzQ29kZV0sXG4gICAgICAgICksXG4gICAgICB9LFxuICAgICAgcmVzcG9uc2UsXG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiByZXNwb25zZS5ib2R5O1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHByb2Nlc3NSZXNwb25zZTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/request.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/request.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const assert = __webpack_require__(/*! assert */ \"assert\");\nconst querystring = __webpack_require__(/*! querystring */ \"querystring\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { once } = __webpack_require__(/*! events */ \"events\");\nconst { URL } = __webpack_require__(/*! url */ \"url\");\n\nconst LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/openid-client/package.json\");\nconst { RPError } = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\n\nconst pick = __webpack_require__(/*! ./pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { deep: defaultsDeep } = __webpack_require__(/*! ./defaults */ \"(rsc)/./node_modules/openid-client/lib/helpers/defaults.js\");\nconst { HTTP_OPTIONS } = __webpack_require__(/*! ./consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\n\nlet DEFAULT_HTTP_OPTIONS;\nconst NQCHAR = /^[\\x21\\x23-\\x5B\\x5D-\\x7E]+$/;\n\nconst allowed = [\n  'agent',\n  'ca',\n  'cert',\n  'crl',\n  'headers',\n  'key',\n  'lookup',\n  'passphrase',\n  'pfx',\n  'timeout',\n];\n\nconst setDefaults = (props, options) => {\n  DEFAULT_HTTP_OPTIONS = defaultsDeep(\n    {},\n    props.length ? pick(options, ...props) : options,\n    DEFAULT_HTTP_OPTIONS,\n  );\n};\n\nsetDefaults([], {\n  headers: {\n    'User-Agent': `${pkg.name}/${pkg.version} (${pkg.homepage})`,\n    'Accept-Encoding': 'identity',\n  },\n  timeout: 3500,\n});\n\nfunction send(req, body, contentType) {\n  if (contentType) {\n    req.removeHeader('content-type');\n    req.setHeader('content-type', contentType);\n  }\n  if (body) {\n    req.removeHeader('content-length');\n    req.setHeader('content-length', Buffer.byteLength(body));\n    req.write(body);\n  }\n  req.end();\n}\n\nconst nonces = new LRU({ max: 100 });\n\nmodule.exports = async function request(options, { accessToken, mTLS = false, DPoP } = {}) {\n  let url;\n  try {\n    url = new URL(options.url);\n    delete options.url;\n    assert(/^(https?:)$/.test(url.protocol));\n  } catch (err) {\n    throw new TypeError('only valid absolute URLs can be requested');\n  }\n  const optsFn = this[HTTP_OPTIONS];\n  let opts = options;\n\n  const nonceKey = `${url.origin}${url.pathname}`;\n  if (DPoP && 'dpopProof' in this) {\n    opts.headers = opts.headers || {};\n    opts.headers.DPoP = await this.dpopProof(\n      {\n        htu: `${url.origin}${url.pathname}`,\n        htm: options.method || 'GET',\n        nonce: nonces.get(nonceKey),\n      },\n      DPoP,\n      accessToken,\n    );\n  }\n\n  let userOptions;\n  if (optsFn) {\n    userOptions = pick(\n      optsFn.call(this, url, defaultsDeep({}, opts, DEFAULT_HTTP_OPTIONS)),\n      ...allowed,\n    );\n  }\n  opts = defaultsDeep({}, userOptions, opts, DEFAULT_HTTP_OPTIONS);\n\n  if (mTLS && !opts.pfx && !(opts.key && opts.cert)) {\n    throw new TypeError('mutual-TLS certificate and key not set');\n  }\n\n  if (opts.searchParams) {\n    for (const [key, value] of Object.entries(opts.searchParams)) {\n      url.searchParams.delete(key);\n      url.searchParams.set(key, value);\n    }\n  }\n\n  let responseType;\n  let form;\n  let json;\n  let body;\n  ({ form, responseType, json, body, ...opts } = opts);\n\n  for (const [key, value] of Object.entries(opts.headers || {})) {\n    if (value === undefined) {\n      delete opts.headers[key];\n    }\n  }\n\n  let response;\n  const req = (url.protocol === 'https:' ? https.request : http.request)(url.href, opts);\n  return (async () => {\n    if (json) {\n      send(req, JSON.stringify(json), 'application/json');\n    } else if (form) {\n      send(req, querystring.stringify(form), 'application/x-www-form-urlencoded');\n    } else if (body) {\n      send(req, body);\n    } else {\n      send(req);\n    }\n\n    [response] = await Promise.race([once(req, 'response'), once(req, 'timeout')]);\n\n    // timeout reached\n    if (!response) {\n      req.destroy();\n      throw new RPError(`outgoing request timed out after ${opts.timeout}ms`);\n    }\n\n    const parts = [];\n\n    for await (const part of response) {\n      parts.push(part);\n    }\n\n    if (parts.length) {\n      switch (responseType) {\n        case 'json': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              let value = Buffer.concat(parts);\n              try {\n                value = JSON.parse(value);\n              } catch (err) {\n                Object.defineProperty(err, 'response', { value: response });\n                throw err;\n              } finally {\n                Object.defineProperty(response, 'body', { value, configurable: true });\n              }\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        case undefined:\n        case 'buffer': {\n          Object.defineProperty(response, 'body', {\n            get() {\n              const value = Buffer.concat(parts);\n              Object.defineProperty(response, 'body', { value, configurable: true });\n              return value;\n            },\n            configurable: true,\n          });\n          break;\n        }\n        default:\n          throw new TypeError('unsupported responseType request option');\n      }\n    }\n\n    return response;\n  })()\n    .catch((err) => {\n      if (response) Object.defineProperty(err, 'response', { value: response });\n      throw err;\n    })\n    .finally(() => {\n      const dpopNonce = response && response.headers['dpop-nonce'];\n      if (dpopNonce && NQCHAR.test(dpopNonce)) {\n        nonces.set(nonceKey, dpopNonce);\n      }\n    });\n};\n\nmodule.exports.setDefaults = setDefaults.bind(undefined, allowed);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js":
/*!******************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/unix_timestamp.js ***!
  \******************************************************************/
/***/ ((module) => {

eval("module.exports = () => Math.floor(Date.now() / 1000);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy91bml4X3RpbWVzdGFtcC5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHVuaXhfdGltZXN0YW1wLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gKCkgPT4gTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js":
/*!**************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/weak_cache.js ***!
  \**************************************************************/
/***/ ((module) => {

eval("module.exports.keystores = new WeakMap();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93ZWFrX2NhY2hlLmpzIiwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QiIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxvcGVuaWQtY2xpZW50XFxsaWJcXGhlbHBlcnNcXHdlYWtfY2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMua2V5c3RvcmVzID0gbmV3IFdlYWtNYXAoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/weak_cache.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js":
/*!***********************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/webfinger_normalize.js ***!
  \***********************************************************************/
/***/ ((module) => {

eval("// Credit: https://github.com/rohe/pyoidc/blob/master/src/oic/utils/webfinger.py\n\n// -- Normalization --\n// A string of any other type is interpreted as a URI either the form of scheme\n// \"://\" authority path-abempty [ \"?\" query ] [ \"#\" fragment ] or authority\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986] and is\n// normalized according to the following rules:\n//\n// If the user input Identifier does not have an RFC 3986 [RFC3986] scheme\n// portion, the string is interpreted as [userinfo \"@\"] host [\":\" port]\n// path-abempty [ \"?\" query ] [ \"#\" fragment ] per RFC 3986 [RFC3986].\n// If the userinfo component is present and all of the path component, query\n// component, and port component are empty, the acct scheme is assumed. In this\n// case, the normalized URI is formed by prefixing acct: to the string as the\n// scheme. Per the 'acct' URI Scheme [I‑D.ietf‑appsawg‑acct‑uri], if there is an\n// at-sign character ('@') in the userinfo component, it needs to be\n// percent-encoded as described in RFC 3986 [RFC3986].\n// For all other inputs without a scheme portion, the https scheme is assumed,\n// and the normalized URI is formed by prefixing https:// to the string as the\n// scheme.\n// If the resulting URI contains a fragment portion, it MUST be stripped off\n// together with the fragment delimiter character \"#\".\n// The WebFinger [I‑D.ietf‑appsawg‑webfinger] Resource in this case is the\n// resulting URI, and the WebFinger Host is the authority component.\n//\n// Note: Since the definition of authority in RFC 3986 [RFC3986] is\n// [ userinfo \"@\" ] host [ \":\" port ], it is legal to have a user input\n// identifier like userinfo@host:port, e.g., <EMAIL>:8080.\n\nconst PORT = /^\\d+$/;\n\nfunction hasScheme(input) {\n  if (input.includes('://')) return true;\n\n  const authority = input.replace(/(\\/|\\?)/g, '#').split('#')[0];\n  if (authority.includes(':')) {\n    const index = authority.indexOf(':');\n    const hostOrPort = authority.slice(index + 1);\n    if (!PORT.test(hostOrPort)) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction acctSchemeAssumed(input) {\n  if (!input.includes('@')) return false;\n  const parts = input.split('@');\n  const host = parts[parts.length - 1];\n  return !(host.includes(':') || host.includes('/') || host.includes('?'));\n}\n\nfunction normalize(input) {\n  if (typeof input !== 'string') {\n    throw new TypeError('input must be a string');\n  }\n\n  let output;\n  if (hasScheme(input)) {\n    output = input;\n  } else if (acctSchemeAssumed(input)) {\n    output = `acct:${input}`;\n  } else {\n    output = `https://${input}`;\n  }\n\n  return output.split('#')[0];\n}\n\nmodule.exports = normalize;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js":
/*!***************************************************************************!*\
  !*** ./node_modules/openid-client/lib/helpers/www_authenticate_parser.js ***!
  \***************************************************************************/
/***/ ((module) => {

eval("const REGEXP = /(\\w+)=(\"[^\"]*\")/g;\n\nmodule.exports = (wwwAuthenticate) => {\n  const params = {};\n  try {\n    while (REGEXP.exec(wwwAuthenticate) !== null) {\n      if (RegExp.$1 && RegExp.$2) {\n        params[RegExp.$1] = RegExp.$2.slice(1, -1);\n      }\n    }\n  } catch (err) {}\n\n  return params;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaGVscGVycy93d3dfYXV0aGVudGljYXRlX3BhcnNlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTs7QUFFSjtBQUNBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcaGVscGVyc1xcd3d3X2F1dGhlbnRpY2F0ZV9wYXJzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgUkVHRVhQID0gLyhcXHcrKT0oXCJbXlwiXSpcIikvZztcblxubW9kdWxlLmV4cG9ydHMgPSAod3d3QXV0aGVudGljYXRlKSA9PiB7XG4gIGNvbnN0IHBhcmFtcyA9IHt9O1xuICB0cnkge1xuICAgIHdoaWxlIChSRUdFWFAuZXhlYyh3d3dBdXRoZW50aWNhdGUpICE9PSBudWxsKSB7XG4gICAgICBpZiAoUmVnRXhwLiQxICYmIFJlZ0V4cC4kMikge1xuICAgICAgICBwYXJhbXNbUmVnRXhwLiQxXSA9IFJlZ0V4cC4kMi5zbGljZSgxLCAtMSk7XG4gICAgICB9XG4gICAgfVxuICB9IGNhdGNoIChlcnIpIHt9XG5cbiAgcmV0dXJuIHBhcmFtcztcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/helpers/www_authenticate_parser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/lib/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const Issuer = __webpack_require__(/*! ./issuer */ \"(rsc)/./node_modules/openid-client/lib/issuer.js\");\nconst { OPError, RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst Strategy = __webpack_require__(/*! ./passport_strategy */ \"(rsc)/./node_modules/openid-client/lib/passport_strategy.js\");\nconst TokenSet = __webpack_require__(/*! ./token_set */ \"(rsc)/./node_modules/openid-client/lib/token_set.js\");\nconst { CLOCK_TOLERANCE, HTTP_OPTIONS } = __webpack_require__(/*! ./helpers/consts */ \"(rsc)/./node_modules/openid-client/lib/helpers/consts.js\");\nconst generators = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst { setDefaults } = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\n\nmodule.exports = {\n  Issuer,\n  Strategy,\n  TokenSet,\n  errors: {\n    OPError,\n    RPError,\n  },\n  custom: {\n    setHttpOptionsDefaults: setDefaults,\n    http_options: HTTP_OPTIONS,\n    clock_tolerance: CLOCK_TOLERANCE,\n  },\n  generators,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUEsZUFBZSxtQkFBTyxDQUFDLGtFQUFVO0FBQ2pDLFFBQVEsbUJBQW1CLEVBQUUsbUJBQU8sQ0FBQyxrRUFBVTtBQUMvQyxpQkFBaUIsbUJBQU8sQ0FBQyx3RkFBcUI7QUFDOUMsaUJBQWlCLG1CQUFPLENBQUMsd0VBQWE7QUFDdEMsUUFBUSxnQ0FBZ0MsRUFBRSxtQkFBTyxDQUFDLGtGQUFrQjtBQUNwRSxtQkFBbUIsbUJBQU8sQ0FBQywwRkFBc0I7QUFDakQsUUFBUSxjQUFjLEVBQUUsbUJBQU8sQ0FBQyxvRkFBbUI7O0FBRW5EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBJc3N1ZXIgPSByZXF1aXJlKCcuL2lzc3VlcicpO1xuY29uc3QgeyBPUEVycm9yLCBSUEVycm9yIH0gPSByZXF1aXJlKCcuL2Vycm9ycycpO1xuY29uc3QgU3RyYXRlZ3kgPSByZXF1aXJlKCcuL3Bhc3Nwb3J0X3N0cmF0ZWd5Jyk7XG5jb25zdCBUb2tlblNldCA9IHJlcXVpcmUoJy4vdG9rZW5fc2V0Jyk7XG5jb25zdCB7IENMT0NLX1RPTEVSQU5DRSwgSFRUUF9PUFRJT05TIH0gPSByZXF1aXJlKCcuL2hlbHBlcnMvY29uc3RzJyk7XG5jb25zdCBnZW5lcmF0b3JzID0gcmVxdWlyZSgnLi9oZWxwZXJzL2dlbmVyYXRvcnMnKTtcbmNvbnN0IHsgc2V0RGVmYXVsdHMgfSA9IHJlcXVpcmUoJy4vaGVscGVycy9yZXF1ZXN0Jyk7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBJc3N1ZXIsXG4gIFN0cmF0ZWd5LFxuICBUb2tlblNldCxcbiAgZXJyb3JzOiB7XG4gICAgT1BFcnJvcixcbiAgICBSUEVycm9yLFxuICB9LFxuICBjdXN0b206IHtcbiAgICBzZXRIdHRwT3B0aW9uc0RlZmF1bHRzOiBzZXREZWZhdWx0cyxcbiAgICBodHRwX29wdGlvbnM6IEhUVFBfT1BUSU9OUyxcbiAgICBjbG9ja190b2xlcmFuY2U6IENMT0NLX1RPTEVSQU5DRSxcbiAgfSxcbiAgZ2VuZXJhdG9ycyxcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer.js":
/*!**************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const { inspect } = __webpack_require__(/*! util */ \"util\");\nconst url = __webpack_require__(/*! url */ \"url\");\n\nconst { RPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst getClient = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst registry = __webpack_require__(/*! ./issuer_registry */ \"(rsc)/./node_modules/openid-client/lib/issuer_registry.js\");\nconst processResponse = __webpack_require__(/*! ./helpers/process_response */ \"(rsc)/./node_modules/openid-client/lib/helpers/process_response.js\");\nconst webfingerNormalize = __webpack_require__(/*! ./helpers/webfinger_normalize */ \"(rsc)/./node_modules/openid-client/lib/helpers/webfinger_normalize.js\");\nconst request = __webpack_require__(/*! ./helpers/request */ \"(rsc)/./node_modules/openid-client/lib/helpers/request.js\");\nconst clone = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { keystore } = __webpack_require__(/*! ./helpers/issuer */ \"(rsc)/./node_modules/openid-client/lib/helpers/issuer.js\");\n\nconst AAD_MULTITENANT_DISCOVERY = [\n  'https://login.microsoftonline.com/common/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration',\n  'https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration',\n];\nconst AAD_MULTITENANT = Symbol();\nconst ISSUER_DEFAULTS = {\n  claim_types_supported: ['normal'],\n  claims_parameter_supported: false,\n  grant_types_supported: ['authorization_code', 'implicit'],\n  request_parameter_supported: false,\n  request_uri_parameter_supported: true,\n  require_request_uri_registration: false,\n  response_modes_supported: ['query', 'fragment'],\n  token_endpoint_auth_methods_supported: ['client_secret_basic'],\n};\n\nclass Issuer {\n  #metadata;\n  constructor(meta = {}) {\n    const aadIssValidation = meta[AAD_MULTITENANT];\n    delete meta[AAD_MULTITENANT];\n    ['introspection', 'revocation'].forEach((endpoint) => {\n      // if intro/revocation endpoint auth specific meta is missing use the token ones if they\n      // are defined\n      if (\n        meta[`${endpoint}_endpoint`] &&\n        meta[`${endpoint}_endpoint_auth_methods_supported`] === undefined &&\n        meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] === undefined\n      ) {\n        if (meta.token_endpoint_auth_methods_supported) {\n          meta[`${endpoint}_endpoint_auth_methods_supported`] =\n            meta.token_endpoint_auth_methods_supported;\n        }\n        if (meta.token_endpoint_auth_signing_alg_values_supported) {\n          meta[`${endpoint}_endpoint_auth_signing_alg_values_supported`] =\n            meta.token_endpoint_auth_signing_alg_values_supported;\n        }\n      }\n    });\n\n    this.#metadata = new Map();\n\n    Object.entries(meta).forEach(([key, value]) => {\n      this.#metadata.set(key, value);\n      if (!this[key]) {\n        Object.defineProperty(this, key, {\n          get() {\n            return this.#metadata.get(key);\n          },\n          enumerable: true,\n        });\n      }\n    });\n\n    registry.set(this.issuer, this);\n\n    const Client = getClient(this, aadIssValidation);\n\n    Object.defineProperties(this, {\n      Client: { value: Client, enumerable: true },\n      FAPI1Client: { value: class FAPI1Client extends Client {}, enumerable: true },\n      FAPI2Client: { value: class FAPI2Client extends Client {}, enumerable: true },\n    });\n  }\n\n  get metadata() {\n    return clone(Object.fromEntries(this.#metadata.entries()));\n  }\n\n  static async webfinger(input) {\n    const resource = webfingerNormalize(input);\n    const { host } = url.parse(resource);\n    const webfingerUrl = `https://${host}/.well-known/webfinger`;\n\n    const response = await request.call(this, {\n      method: 'GET',\n      url: webfingerUrl,\n      responseType: 'json',\n      searchParams: { resource, rel: 'http://openid.net/specs/connect/1.0/issuer' },\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n\n    const location =\n      Array.isArray(body.links) &&\n      body.links.find(\n        (link) =>\n          typeof link === 'object' &&\n          link.rel === 'http://openid.net/specs/connect/1.0/issuer' &&\n          link.href,\n      );\n\n    if (!location) {\n      throw new RPError({\n        message: 'no issuer found in webfinger response',\n        body,\n      });\n    }\n\n    if (typeof location.href !== 'string' || !location.href.startsWith('https://')) {\n      throw new RPError({\n        printf: ['invalid issuer location %s', location.href],\n        body,\n      });\n    }\n\n    const expectedIssuer = location.href;\n    if (registry.has(expectedIssuer)) {\n      return registry.get(expectedIssuer);\n    }\n\n    const issuer = await this.discover(expectedIssuer);\n\n    if (issuer.issuer !== expectedIssuer) {\n      registry.del(issuer.issuer);\n      throw new RPError(\n        'discovered issuer mismatch, expected %s, got: %s',\n        expectedIssuer,\n        issuer.issuer,\n      );\n    }\n    return issuer;\n  }\n\n  static async discover(uri) {\n    const wellKnownUri = resolveWellKnownUri(uri);\n\n    const response = await request.call(this, {\n      method: 'GET',\n      responseType: 'json',\n      url: wellKnownUri,\n      headers: {\n        Accept: 'application/json',\n      },\n    });\n    const body = processResponse(response);\n    return new Issuer({\n      ...ISSUER_DEFAULTS,\n      ...body,\n      [AAD_MULTITENANT]: !!AAD_MULTITENANT_DISCOVERY.find((discoveryURL) =>\n        wellKnownUri.startsWith(discoveryURL),\n      ),\n    });\n  }\n\n  async reloadJwksUri() {\n    await keystore.call(this, true);\n  }\n\n  /* istanbul ignore next */\n  [inspect.custom]() {\n    return `${this.constructor.name} ${inspect(this.metadata, {\n      depth: Infinity,\n      colors: process.stdout.isTTY,\n      compact: false,\n      sorted: true,\n    })}`;\n  }\n}\n\nfunction resolveWellKnownUri(uri) {\n  const parsed = url.parse(uri);\n  if (parsed.pathname.includes('/.well-known/')) {\n    return uri;\n  } else {\n    let pathname;\n    if (parsed.pathname.endsWith('/')) {\n      pathname = `${parsed.pathname}.well-known/openid-configuration`;\n    } else {\n      pathname = `${parsed.pathname}/.well-known/openid-configuration`;\n    }\n    return url.format({ ...parsed, pathname });\n  }\n}\n\nmodule.exports = Issuer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/issuer_registry.js":
/*!***********************************************************!*\
  !*** ./node_modules/openid-client/lib/issuer_registry.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const LRU = __webpack_require__(/*! lru-cache */ \"(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\");\n\nmodule.exports = new LRU({ max: 100 });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvaXNzdWVyX3JlZ2lzdHJ5LmpzIiwibWFwcGluZ3MiOiJBQUFBLFlBQVksbUJBQU8sQ0FBQyxxRkFBVzs7QUFFL0IsMkJBQTJCLFVBQVUiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcb3BlbmlkLWNsaWVudFxcbGliXFxpc3N1ZXJfcmVnaXN0cnkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgTFJVID0gcmVxdWlyZSgnbHJ1LWNhY2hlJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gbmV3IExSVSh7IG1heDogMTAwIH0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/issuer_registry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/passport_strategy.js":
/*!*************************************************************!*\
  !*** ./node_modules/openid-client/lib/passport_strategy.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const url = __webpack_require__(/*! url */ \"url\");\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\nconst cloneDeep = __webpack_require__(/*! ./helpers/deep_clone */ \"(rsc)/./node_modules/openid-client/lib/helpers/deep_clone.js\");\nconst { RPError, OPError } = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/openid-client/lib/errors.js\");\nconst { BaseClient } = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/openid-client/lib/client.js\");\nconst { random, codeChallenge } = __webpack_require__(/*! ./helpers/generators */ \"(rsc)/./node_modules/openid-client/lib/helpers/generators.js\");\nconst pick = __webpack_require__(/*! ./helpers/pick */ \"(rsc)/./node_modules/openid-client/lib/helpers/pick.js\");\nconst { resolveResponseType, resolveRedirectUri } = __webpack_require__(/*! ./helpers/client */ \"(rsc)/./node_modules/openid-client/lib/helpers/client.js\");\n\nfunction verified(err, user, info = {}) {\n  if (err) {\n    this.error(err);\n  } else if (!user) {\n    this.fail(info);\n  } else {\n    this.success(user, info);\n  }\n}\n\nfunction OpenIDConnectStrategy(\n  { client, params = {}, passReqToCallback = false, sessionKey, usePKCE = true, extras = {} } = {},\n  verify,\n) {\n  if (!(client instanceof BaseClient)) {\n    throw new TypeError('client must be an instance of openid-client Client');\n  }\n\n  if (typeof verify !== 'function') {\n    throw new TypeError('verify callback must be a function');\n  }\n\n  if (!client.issuer || !client.issuer.issuer) {\n    throw new TypeError('client must have an issuer with an identifier');\n  }\n\n  this._client = client;\n  this._issuer = client.issuer;\n  this._verify = verify;\n  this._passReqToCallback = passReqToCallback;\n  this._usePKCE = usePKCE;\n  this._key = sessionKey || `oidc:${url.parse(this._issuer.issuer).hostname}`;\n  this._params = cloneDeep(params);\n\n  // state and nonce are handled in authenticate()\n  delete this._params.state;\n  delete this._params.nonce;\n\n  this._extras = cloneDeep(extras);\n\n  if (!this._params.response_type) this._params.response_type = resolveResponseType.call(client);\n  if (!this._params.redirect_uri) this._params.redirect_uri = resolveRedirectUri.call(client);\n  if (!this._params.scope) this._params.scope = 'openid';\n\n  if (this._usePKCE === true) {\n    const supportedMethods = Array.isArray(this._issuer.code_challenge_methods_supported)\n      ? this._issuer.code_challenge_methods_supported\n      : false;\n\n    if (supportedMethods && supportedMethods.includes('S256')) {\n      this._usePKCE = 'S256';\n    } else if (supportedMethods && supportedMethods.includes('plain')) {\n      this._usePKCE = 'plain';\n    } else if (supportedMethods) {\n      throw new TypeError(\n        'neither code_challenge_method supported by the client is supported by the issuer',\n      );\n    } else {\n      this._usePKCE = 'S256';\n    }\n  } else if (typeof this._usePKCE === 'string' && !['plain', 'S256'].includes(this._usePKCE)) {\n    throw new TypeError(`${this._usePKCE} is not valid/implemented PKCE code_challenge_method`);\n  }\n\n  this.name = url.parse(client.issuer.issuer).hostname;\n}\n\nOpenIDConnectStrategy.prototype.authenticate = function authenticate(req, options) {\n  (async () => {\n    const client = this._client;\n    if (!req.session) {\n      throw new TypeError('authentication requires session support');\n    }\n    const reqParams = client.callbackParams(req);\n    const sessionKey = this._key;\n\n    const { 0: parameter, length } = Object.keys(reqParams);\n\n    /**\n     * Start authentication request if this has no authorization response parameters or\n     * this might a login initiated from a third party as per\n     * https://openid.net/specs/openid-connect-core-1_0.html#ThirdPartyInitiatedLogin.\n     */\n    if (length === 0 || (length === 1 && parameter === 'iss')) {\n      // provide options object with extra authentication parameters\n      const params = {\n        state: random(),\n        ...this._params,\n        ...options,\n      };\n\n      if (!params.nonce && params.response_type.includes('id_token')) {\n        params.nonce = random();\n      }\n\n      req.session[sessionKey] = pick(params, 'nonce', 'state', 'max_age', 'response_type');\n\n      if (this._usePKCE && params.response_type.includes('code')) {\n        const verifier = random();\n        req.session[sessionKey].code_verifier = verifier;\n\n        switch (this._usePKCE) {\n          case 'S256':\n            params.code_challenge = codeChallenge(verifier);\n            params.code_challenge_method = 'S256';\n            break;\n          case 'plain':\n            params.code_challenge = verifier;\n            break;\n        }\n      }\n\n      this.redirect(client.authorizationUrl(params));\n      return;\n    }\n    /* end authentication request */\n\n    /* start authentication response */\n\n    const session = req.session[sessionKey];\n    if (Object.keys(session || {}).length === 0) {\n      throw new Error(\n        format(\n          'did not find expected authorization request details in session, req.session[\"%s\"] is %j',\n          sessionKey,\n          session,\n        ),\n      );\n    }\n\n    const {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    } = session;\n\n    try {\n      delete req.session[sessionKey];\n    } catch (err) {}\n\n    const opts = {\n      redirect_uri: this._params.redirect_uri,\n      ...options,\n    };\n\n    const checks = {\n      state,\n      nonce,\n      max_age: maxAge,\n      code_verifier: codeVerifier,\n      response_type: responseType,\n    };\n\n    const tokenset = await client.callback(opts.redirect_uri, reqParams, checks, this._extras);\n\n    const passReq = this._passReqToCallback;\n    const loadUserinfo = this._verify.length > (passReq ? 3 : 2) && client.issuer.userinfo_endpoint;\n\n    const args = [tokenset, verified.bind(this)];\n\n    if (loadUserinfo) {\n      if (!tokenset.access_token) {\n        throw new RPError({\n          message:\n            'expected access_token to be returned when asking for userinfo in verify callback',\n          tokenset,\n        });\n      }\n      const userinfo = await client.userinfo(tokenset);\n      args.splice(1, 0, userinfo);\n    }\n\n    if (passReq) {\n      args.unshift(req);\n    }\n\n    this._verify(...args);\n    /* end authentication response */\n  })().catch((error) => {\n    if (\n      (error instanceof OPError &&\n        error.error !== 'server_error' &&\n        !error.error.startsWith('invalid')) ||\n      error instanceof RPError\n    ) {\n      this.fail(error);\n    } else {\n      this.error(error);\n    }\n  });\n};\n\nmodule.exports = OpenIDConnectStrategy;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/passport_strategy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/lib/token_set.js":
/*!*****************************************************!*\
  !*** ./node_modules/openid-client/lib/token_set.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const base64url = __webpack_require__(/*! ./helpers/base64url */ \"(rsc)/./node_modules/openid-client/lib/helpers/base64url.js\");\nconst now = __webpack_require__(/*! ./helpers/unix_timestamp */ \"(rsc)/./node_modules/openid-client/lib/helpers/unix_timestamp.js\");\n\nclass TokenSet {\n  constructor(values) {\n    Object.assign(this, values);\n    const { constructor, ...properties } = Object.getOwnPropertyDescriptors(\n      this.constructor.prototype,\n    );\n\n    Object.defineProperties(this, properties);\n  }\n\n  set expires_in(value) {\n    this.expires_at = now() + Number(value);\n  }\n\n  get expires_in() {\n    return Math.max.apply(null, [this.expires_at - now(), 0]);\n  }\n\n  expired() {\n    return this.expires_in === 0;\n  }\n\n  claims() {\n    if (!this.id_token) {\n      throw new TypeError('id_token not present in TokenSet');\n    }\n\n    return JSON.parse(base64url.decode(this.id_token.split('.')[1]));\n  }\n}\n\nmodule.exports = TokenSet;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvb3BlbmlkLWNsaWVudC9saWIvdG9rZW5fc2V0LmpzIiwibWFwcGluZ3MiOiJBQUFBLGtCQUFrQixtQkFBTyxDQUFDLHdGQUFxQjtBQUMvQyxZQUFZLG1CQUFPLENBQUMsa0dBQTBCOztBQUU5QztBQUNBO0FBQ0E7QUFDQSxZQUFZLDZCQUE2QjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG9wZW5pZC1jbGllbnRcXGxpYlxcdG9rZW5fc2V0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhc2U2NHVybCA9IHJlcXVpcmUoJy4vaGVscGVycy9iYXNlNjR1cmwnKTtcbmNvbnN0IG5vdyA9IHJlcXVpcmUoJy4vaGVscGVycy91bml4X3RpbWVzdGFtcCcpO1xuXG5jbGFzcyBUb2tlblNldCB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlcykge1xuICAgIE9iamVjdC5hc3NpZ24odGhpcywgdmFsdWVzKTtcbiAgICBjb25zdCB7IGNvbnN0cnVjdG9yLCAuLi5wcm9wZXJ0aWVzIH0gPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycyhcbiAgICAgIHRoaXMuY29uc3RydWN0b3IucHJvdG90eXBlLFxuICAgICk7XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydGllcyh0aGlzLCBwcm9wZXJ0aWVzKTtcbiAgfVxuXG4gIHNldCBleHBpcmVzX2luKHZhbHVlKSB7XG4gICAgdGhpcy5leHBpcmVzX2F0ID0gbm93KCkgKyBOdW1iZXIodmFsdWUpO1xuICB9XG5cbiAgZ2V0IGV4cGlyZXNfaW4oKSB7XG4gICAgcmV0dXJuIE1hdGgubWF4LmFwcGx5KG51bGwsIFt0aGlzLmV4cGlyZXNfYXQgLSBub3coKSwgMF0pO1xuICB9XG5cbiAgZXhwaXJlZCgpIHtcbiAgICByZXR1cm4gdGhpcy5leHBpcmVzX2luID09PSAwO1xuICB9XG5cbiAgY2xhaW1zKCkge1xuICAgIGlmICghdGhpcy5pZF90b2tlbikge1xuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignaWRfdG9rZW4gbm90IHByZXNlbnQgaW4gVG9rZW5TZXQnKTtcbiAgICB9XG5cbiAgICByZXR1cm4gSlNPTi5wYXJzZShiYXNlNjR1cmwuZGVjb2RlKHRoaXMuaWRfdG9rZW4uc3BsaXQoJy4nKVsxXSkpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5TZXQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/lib/token_set.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/lru-cache/index.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\n// A linked list to keep track of recently-used-ness\nconst Yallist = __webpack_require__(/*! yallist */ \"(rsc)/./node_modules/yallist/yallist.js\")\n\nconst MAX = Symbol('max')\nconst LENGTH = Symbol('length')\nconst LENGTH_CALCULATOR = Symbol('lengthCalculator')\nconst ALLOW_STALE = Symbol('allowStale')\nconst MAX_AGE = Symbol('maxAge')\nconst DISPOSE = Symbol('dispose')\nconst NO_DISPOSE_ON_SET = Symbol('noDisposeOnSet')\nconst LRU_LIST = Symbol('lruList')\nconst CACHE = Symbol('cache')\nconst UPDATE_AGE_ON_GET = Symbol('updateAgeOnGet')\n\nconst naiveLength = () => 1\n\n// lruList is a yallist where the head is the youngest\n// item, and the tail is the oldest.  the list contains the Hit\n// objects as the entries.\n// Each Hit object has a reference to its Yallist.Node.  This\n// never changes.\n//\n// cache is a Map (or PseudoMap) that matches the keys to\n// the Yallist.Node object.\nclass LRUCache {\n  constructor (options) {\n    if (typeof options === 'number')\n      options = { max: options }\n\n    if (!options)\n      options = {}\n\n    if (options.max && (typeof options.max !== 'number' || options.max < 0))\n      throw new TypeError('max must be a non-negative number')\n    // Kind of weird to have a default max of Infinity, but oh well.\n    const max = this[MAX] = options.max || Infinity\n\n    const lc = options.length || naiveLength\n    this[LENGTH_CALCULATOR] = (typeof lc !== 'function') ? naiveLength : lc\n    this[ALLOW_STALE] = options.stale || false\n    if (options.maxAge && typeof options.maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n    this[MAX_AGE] = options.maxAge || 0\n    this[DISPOSE] = options.dispose\n    this[NO_DISPOSE_ON_SET] = options.noDisposeOnSet || false\n    this[UPDATE_AGE_ON_GET] = options.updateAgeOnGet || false\n    this.reset()\n  }\n\n  // resize the cache when the max changes.\n  set max (mL) {\n    if (typeof mL !== 'number' || mL < 0)\n      throw new TypeError('max must be a non-negative number')\n\n    this[MAX] = mL || Infinity\n    trim(this)\n  }\n  get max () {\n    return this[MAX]\n  }\n\n  set allowStale (allowStale) {\n    this[ALLOW_STALE] = !!allowStale\n  }\n  get allowStale () {\n    return this[ALLOW_STALE]\n  }\n\n  set maxAge (mA) {\n    if (typeof mA !== 'number')\n      throw new TypeError('maxAge must be a non-negative number')\n\n    this[MAX_AGE] = mA\n    trim(this)\n  }\n  get maxAge () {\n    return this[MAX_AGE]\n  }\n\n  // resize the cache when the lengthCalculator changes.\n  set lengthCalculator (lC) {\n    if (typeof lC !== 'function')\n      lC = naiveLength\n\n    if (lC !== this[LENGTH_CALCULATOR]) {\n      this[LENGTH_CALCULATOR] = lC\n      this[LENGTH] = 0\n      this[LRU_LIST].forEach(hit => {\n        hit.length = this[LENGTH_CALCULATOR](hit.value, hit.key)\n        this[LENGTH] += hit.length\n      })\n    }\n    trim(this)\n  }\n  get lengthCalculator () { return this[LENGTH_CALCULATOR] }\n\n  get length () { return this[LENGTH] }\n  get itemCount () { return this[LRU_LIST].length }\n\n  rforEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].tail; walker !== null;) {\n      const prev = walker.prev\n      forEachStep(this, fn, walker, thisp)\n      walker = prev\n    }\n  }\n\n  forEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].head; walker !== null;) {\n      const next = walker.next\n      forEachStep(this, fn, walker, thisp)\n      walker = next\n    }\n  }\n\n  keys () {\n    return this[LRU_LIST].toArray().map(k => k.key)\n  }\n\n  values () {\n    return this[LRU_LIST].toArray().map(k => k.value)\n  }\n\n  reset () {\n    if (this[DISPOSE] &&\n        this[LRU_LIST] &&\n        this[LRU_LIST].length) {\n      this[LRU_LIST].forEach(hit => this[DISPOSE](hit.key, hit.value))\n    }\n\n    this[CACHE] = new Map() // hash of items by key\n    this[LRU_LIST] = new Yallist() // list of items in order of use recency\n    this[LENGTH] = 0 // length of items in the list\n  }\n\n  dump () {\n    return this[LRU_LIST].map(hit =>\n      isStale(this, hit) ? false : {\n        k: hit.key,\n        v: hit.value,\n        e: hit.now + (hit.maxAge || 0)\n      }).toArray().filter(h => h)\n  }\n\n  dumpLru () {\n    return this[LRU_LIST]\n  }\n\n  set (key, value, maxAge) {\n    maxAge = maxAge || this[MAX_AGE]\n\n    if (maxAge && typeof maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n\n    const now = maxAge ? Date.now() : 0\n    const len = this[LENGTH_CALCULATOR](value, key)\n\n    if (this[CACHE].has(key)) {\n      if (len > this[MAX]) {\n        del(this, this[CACHE].get(key))\n        return false\n      }\n\n      const node = this[CACHE].get(key)\n      const item = node.value\n\n      // dispose of the old one before overwriting\n      // split out into 2 ifs for better coverage tracking\n      if (this[DISPOSE]) {\n        if (!this[NO_DISPOSE_ON_SET])\n          this[DISPOSE](key, item.value)\n      }\n\n      item.now = now\n      item.maxAge = maxAge\n      item.value = value\n      this[LENGTH] += len - item.length\n      item.length = len\n      this.get(key)\n      trim(this)\n      return true\n    }\n\n    const hit = new Entry(key, value, len, now, maxAge)\n\n    // oversized objects fall out of cache automatically.\n    if (hit.length > this[MAX]) {\n      if (this[DISPOSE])\n        this[DISPOSE](key, value)\n\n      return false\n    }\n\n    this[LENGTH] += hit.length\n    this[LRU_LIST].unshift(hit)\n    this[CACHE].set(key, this[LRU_LIST].head)\n    trim(this)\n    return true\n  }\n\n  has (key) {\n    if (!this[CACHE].has(key)) return false\n    const hit = this[CACHE].get(key).value\n    return !isStale(this, hit)\n  }\n\n  get (key) {\n    return get(this, key, true)\n  }\n\n  peek (key) {\n    return get(this, key, false)\n  }\n\n  pop () {\n    const node = this[LRU_LIST].tail\n    if (!node)\n      return null\n\n    del(this, node)\n    return node.value\n  }\n\n  del (key) {\n    del(this, this[CACHE].get(key))\n  }\n\n  load (arr) {\n    // reset the cache\n    this.reset()\n\n    const now = Date.now()\n    // A previous serialized cache has the most recent items first\n    for (let l = arr.length - 1; l >= 0; l--) {\n      const hit = arr[l]\n      const expiresAt = hit.e || 0\n      if (expiresAt === 0)\n        // the item was created without expiration in a non aged cache\n        this.set(hit.k, hit.v)\n      else {\n        const maxAge = expiresAt - now\n        // dont add already expired items\n        if (maxAge > 0) {\n          this.set(hit.k, hit.v, maxAge)\n        }\n      }\n    }\n  }\n\n  prune () {\n    this[CACHE].forEach((value, key) => get(this, key, false))\n  }\n}\n\nconst get = (self, key, doUse) => {\n  const node = self[CACHE].get(key)\n  if (node) {\n    const hit = node.value\n    if (isStale(self, hit)) {\n      del(self, node)\n      if (!self[ALLOW_STALE])\n        return undefined\n    } else {\n      if (doUse) {\n        if (self[UPDATE_AGE_ON_GET])\n          node.value.now = Date.now()\n        self[LRU_LIST].unshiftNode(node)\n      }\n    }\n    return hit.value\n  }\n}\n\nconst isStale = (self, hit) => {\n  if (!hit || (!hit.maxAge && !self[MAX_AGE]))\n    return false\n\n  const diff = Date.now() - hit.now\n  return hit.maxAge ? diff > hit.maxAge\n    : self[MAX_AGE] && (diff > self[MAX_AGE])\n}\n\nconst trim = self => {\n  if (self[LENGTH] > self[MAX]) {\n    for (let walker = self[LRU_LIST].tail;\n      self[LENGTH] > self[MAX] && walker !== null;) {\n      // We know that we're about to delete this one, and also\n      // what the next least recently used key will be, so just\n      // go ahead and set it now.\n      const prev = walker.prev\n      del(self, walker)\n      walker = prev\n    }\n  }\n}\n\nconst del = (self, node) => {\n  if (node) {\n    const hit = node.value\n    if (self[DISPOSE])\n      self[DISPOSE](hit.key, hit.value)\n\n    self[LENGTH] -= hit.length\n    self[CACHE].delete(hit.key)\n    self[LRU_LIST].removeNode(node)\n  }\n}\n\nclass Entry {\n  constructor (key, value, length, now, maxAge) {\n    this.key = key\n    this.value = value\n    this.length = length\n    this.now = now\n    this.maxAge = maxAge || 0\n  }\n}\n\nconst forEachStep = (self, fn, node, thisp) => {\n  let hit = node.value\n  if (isStale(self, hit)) {\n    del(self, node)\n    if (!self[ALLOW_STALE])\n      hit = undefined\n  }\n  if (hit)\n    fn.call(thisp, hit.value, hit.key, self)\n}\n\nmodule.exports = LRUCache\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/node_modules/lru-cache/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/node_modules/object-hash/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/openid-client/node_modules/object-hash/index.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\n/**\n * Exported function\n *\n * Options:\n *\n *  - `algorithm` hash algo to be used by this instance: *'sha1', 'md5'\n *  - `excludeValues` {true|*false} hash object keys, values ignored\n *  - `encoding` hash encoding, supports 'buffer', '*hex', 'binary', 'base64'\n *  - `ignoreUnknown` {true|*false} ignore unknown object types\n *  - `replacer` optional function that replaces values before hashing\n *  - `respectFunctionProperties` {*true|false} consider function properties when hashing\n *  - `respectFunctionNames` {*true|false} consider 'name' property of functions for hashing\n *  - `respectType` {*true|false} Respect special properties (prototype, constructor)\n *    when hashing to distinguish between types\n *  - `unorderedArrays` {true|*false} Sort all arrays before hashing\n *  - `unorderedSets` {*true|false} Sort `Set` and `Map` instances before hashing\n *  * = default\n *\n * @param {object} object value to hash\n * @param {object} options hashing options\n * @return {string} hash value\n * @api public\n */\nexports = module.exports = objectHash;\n\nfunction objectHash(object, options){\n  options = applyDefaults(object, options);\n\n  return hash(object, options);\n}\n\n/**\n * Exported sugar methods\n *\n * @param {object} object value to hash\n * @return {string} hash value\n * @api public\n */\nexports.sha1 = function(object){\n  return objectHash(object);\n};\nexports.keys = function(object){\n  return objectHash(object, {excludeValues: true, algorithm: 'sha1', encoding: 'hex'});\n};\nexports.MD5 = function(object){\n  return objectHash(object, {algorithm: 'md5', encoding: 'hex'});\n};\nexports.keysMD5 = function(object){\n  return objectHash(object, {algorithm: 'md5', encoding: 'hex', excludeValues: true});\n};\n\n// Internals\nvar hashes = crypto.getHashes ? crypto.getHashes().slice() : ['sha1', 'md5'];\nhashes.push('passthrough');\nvar encodings = ['buffer', 'hex', 'binary', 'base64'];\n\nfunction applyDefaults(object, sourceOptions){\n  sourceOptions = sourceOptions || {};\n\n  // create a copy rather than mutating\n  var options = {};\n  options.algorithm = sourceOptions.algorithm || 'sha1';\n  options.encoding = sourceOptions.encoding || 'hex';\n  options.excludeValues = sourceOptions.excludeValues ? true : false;\n  options.algorithm = options.algorithm.toLowerCase();\n  options.encoding = options.encoding.toLowerCase();\n  options.ignoreUnknown = sourceOptions.ignoreUnknown !== true ? false : true; // default to false\n  options.respectType = sourceOptions.respectType === false ? false : true; // default to true\n  options.respectFunctionNames = sourceOptions.respectFunctionNames === false ? false : true;\n  options.respectFunctionProperties = sourceOptions.respectFunctionProperties === false ? false : true;\n  options.unorderedArrays = sourceOptions.unorderedArrays !== true ? false : true; // default to false\n  options.unorderedSets = sourceOptions.unorderedSets === false ? false : true; // default to false\n  options.unorderedObjects = sourceOptions.unorderedObjects === false ? false : true; // default to true\n  options.replacer = sourceOptions.replacer || undefined;\n  options.excludeKeys = sourceOptions.excludeKeys || undefined;\n\n  if(typeof object === 'undefined') {\n    throw new Error('Object argument required.');\n  }\n\n  // if there is a case-insensitive match in the hashes list, accept it\n  // (i.e. SHA256 for sha256)\n  for (var i = 0; i < hashes.length; ++i) {\n    if (hashes[i].toLowerCase() === options.algorithm.toLowerCase()) {\n      options.algorithm = hashes[i];\n    }\n  }\n\n  if(hashes.indexOf(options.algorithm) === -1){\n    throw new Error('Algorithm \"' + options.algorithm + '\"  not supported. ' +\n      'supported values: ' + hashes.join(', '));\n  }\n\n  if(encodings.indexOf(options.encoding) === -1 &&\n     options.algorithm !== 'passthrough'){\n    throw new Error('Encoding \"' + options.encoding + '\"  not supported. ' +\n      'supported values: ' + encodings.join(', '));\n  }\n\n  return options;\n}\n\n/** Check if the given function is a native function */\nfunction isNativeFunction(f) {\n  if ((typeof f) !== 'function') {\n    return false;\n  }\n  var exp = /^function\\s+\\w*\\s*\\(\\s*\\)\\s*{\\s+\\[native code\\]\\s+}$/i;\n  return exp.exec(Function.prototype.toString.call(f)) != null;\n}\n\nfunction hash(object, options) {\n  var hashingStream;\n\n  if (options.algorithm !== 'passthrough') {\n    hashingStream = crypto.createHash(options.algorithm);\n  } else {\n    hashingStream = new PassThrough();\n  }\n\n  if (typeof hashingStream.write === 'undefined') {\n    hashingStream.write = hashingStream.update;\n    hashingStream.end   = hashingStream.update;\n  }\n\n  var hasher = typeHasher(options, hashingStream);\n  hasher.dispatch(object);\n  if (!hashingStream.update) {\n    hashingStream.end('');\n  }\n\n  if (hashingStream.digest) {\n    return hashingStream.digest(options.encoding === 'buffer' ? undefined : options.encoding);\n  }\n\n  var buf = hashingStream.read();\n  if (options.encoding === 'buffer') {\n    return buf;\n  }\n\n  return buf.toString(options.encoding);\n}\n\n/**\n * Expose streaming API\n *\n * @param {object} object  Value to serialize\n * @param {object} options  Options, as for hash()\n * @param {object} stream  A stream to write the serializiation to\n * @api public\n */\nexports.writeToStream = function(object, options, stream) {\n  if (typeof stream === 'undefined') {\n    stream = options;\n    options = {};\n  }\n\n  options = applyDefaults(object, options);\n\n  return typeHasher(options, stream).dispatch(object);\n};\n\nfunction typeHasher(options, writeTo, context){\n  context = context || [];\n  var write = function(str) {\n    if (writeTo.update) {\n      return writeTo.update(str, 'utf8');\n    } else {\n      return writeTo.write(str, 'utf8');\n    }\n  };\n\n  return {\n    dispatch: function(value){\n      if (options.replacer) {\n        value = options.replacer(value);\n      }\n\n      var type = typeof value;\n      if (value === null) {\n        type = 'null';\n      }\n\n      //console.log(\"[DEBUG] Dispatch: \", value, \"->\", type, \" -> \", \"_\" + type);\n\n      return this['_' + type](value);\n    },\n    _object: function(object) {\n      var pattern = (/\\[object (.*)\\]/i);\n      var objString = Object.prototype.toString.call(object);\n      var objType = pattern.exec(objString);\n      if (!objType) { // object type did not match [object ...]\n        objType = 'unknown:[' + objString + ']';\n      } else {\n        objType = objType[1]; // take only the class name\n      }\n\n      objType = objType.toLowerCase();\n\n      var objectNumber = null;\n\n      if ((objectNumber = context.indexOf(object)) >= 0) {\n        return this.dispatch('[CIRCULAR:' + objectNumber + ']');\n      } else {\n        context.push(object);\n      }\n\n      if (typeof Buffer !== 'undefined' && Buffer.isBuffer && Buffer.isBuffer(object)) {\n        write('buffer:');\n        return write(object);\n      }\n\n      if(objType !== 'object' && objType !== 'function' && objType !== 'asyncfunction') {\n        if(this['_' + objType]) {\n          this['_' + objType](object);\n        } else if (options.ignoreUnknown) {\n          return write('[' + objType + ']');\n        } else {\n          throw new Error('Unknown object type \"' + objType + '\"');\n        }\n      }else{\n        var keys = Object.keys(object);\n        if (options.unorderedObjects) {\n          keys = keys.sort();\n        }\n        // Make sure to incorporate special properties, so\n        // Types with different prototypes will produce\n        // a different hash and objects derived from\n        // different functions (`new Foo`, `new Bar`) will\n        // produce different hashes.\n        // We never do this for native functions since some\n        // seem to break because of that.\n        if (options.respectType !== false && !isNativeFunction(object)) {\n          keys.splice(0, 0, 'prototype', '__proto__', 'constructor');\n        }\n\n        if (options.excludeKeys) {\n          keys = keys.filter(function(key) { return !options.excludeKeys(key); });\n        }\n\n        write('object:' + keys.length + ':');\n        var self = this;\n        return keys.forEach(function(key){\n          self.dispatch(key);\n          write(':');\n          if(!options.excludeValues) {\n            self.dispatch(object[key]);\n          }\n          write(',');\n        });\n      }\n    },\n    _array: function(arr, unordered){\n      unordered = typeof unordered !== 'undefined' ? unordered :\n        options.unorderedArrays !== false; // default to options.unorderedArrays\n\n      var self = this;\n      write('array:' + arr.length + ':');\n      if (!unordered || arr.length <= 1) {\n        return arr.forEach(function(entry) {\n          return self.dispatch(entry);\n        });\n      }\n\n      // the unordered case is a little more complicated:\n      // since there is no canonical ordering on objects,\n      // i.e. {a:1} < {a:2} and {a:1} > {a:2} are both false,\n      // we first serialize each entry using a PassThrough stream\n      // before sorting.\n      // also: we can’t use the same context array for all entries\n      // since the order of hashing should *not* matter. instead,\n      // we keep track of the additions to a copy of the context array\n      // and add all of them to the global context array when we’re done\n      var contextAdditions = [];\n      var entries = arr.map(function(entry) {\n        var strm = new PassThrough();\n        var localContext = context.slice(); // make copy\n        var hasher = typeHasher(options, strm, localContext);\n        hasher.dispatch(entry);\n        // take only what was added to localContext and append it to contextAdditions\n        contextAdditions = contextAdditions.concat(localContext.slice(context.length));\n        return strm.read().toString();\n      });\n      context = context.concat(contextAdditions);\n      entries.sort();\n      return this._array(entries, false);\n    },\n    _date: function(date){\n      return write('date:' + date.toJSON());\n    },\n    _symbol: function(sym){\n      return write('symbol:' + sym.toString());\n    },\n    _error: function(err){\n      return write('error:' + err.toString());\n    },\n    _boolean: function(bool){\n      return write('bool:' + bool.toString());\n    },\n    _string: function(string){\n      write('string:' + string.length + ':');\n      write(string.toString());\n    },\n    _function: function(fn){\n      write('fn:');\n      if (isNativeFunction(fn)) {\n        this.dispatch('[native]');\n      } else {\n        this.dispatch(fn.toString());\n      }\n\n      if (options.respectFunctionNames !== false) {\n        // Make sure we can still distinguish native functions\n        // by their name, otherwise String and Function will\n        // have the same hash\n        this.dispatch(\"function-name:\" + String(fn.name));\n      }\n\n      if (options.respectFunctionProperties) {\n        this._object(fn);\n      }\n    },\n    _number: function(number){\n      return write('number:' + number.toString());\n    },\n    _xml: function(xml){\n      return write('xml:' + xml.toString());\n    },\n    _null: function() {\n      return write('Null');\n    },\n    _undefined: function() {\n      return write('Undefined');\n    },\n    _regexp: function(regex){\n      return write('regex:' + regex.toString());\n    },\n    _uint8array: function(arr){\n      write('uint8array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _uint8clampedarray: function(arr){\n      write('uint8clampedarray:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _int8array: function(arr){\n      write('uint8array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _uint16array: function(arr){\n      write('uint16array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _int16array: function(arr){\n      write('uint16array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _uint32array: function(arr){\n      write('uint32array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _int32array: function(arr){\n      write('uint32array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _float32array: function(arr){\n      write('float32array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _float64array: function(arr){\n      write('float64array:');\n      return this.dispatch(Array.prototype.slice.call(arr));\n    },\n    _arraybuffer: function(arr){\n      write('arraybuffer:');\n      return this.dispatch(new Uint8Array(arr));\n    },\n    _url: function(url) {\n      return write('url:' + url.toString(), 'utf8');\n    },\n    _map: function(map) {\n      write('map:');\n      var arr = Array.from(map);\n      return this._array(arr, options.unorderedSets !== false);\n    },\n    _set: function(set) {\n      write('set:');\n      var arr = Array.from(set);\n      return this._array(arr, options.unorderedSets !== false);\n    },\n    _file: function(file) {\n      write('file:');\n      return this.dispatch([file.name, file.size, file.type, file.lastModfied]);\n    },\n    _blob: function() {\n      if (options.ignoreUnknown) {\n        return write('[blob]');\n      }\n\n      throw Error('Hashing Blob objects is currently not supported\\n' +\n        '(see https://github.com/puleos/object-hash/issues/26)\\n' +\n        'Use \"options.replacer\" or \"options.ignoreUnknown\"\\n');\n    },\n    _domwindow: function() { return write('domwindow'); },\n    _bigint: function(number){\n      return write('bigint:' + number.toString());\n    },\n    /* Node.js standard native objects */\n    _process: function() { return write('process'); },\n    _timer: function() { return write('timer'); },\n    _pipe: function() { return write('pipe'); },\n    _tcp: function() { return write('tcp'); },\n    _udp: function() { return write('udp'); },\n    _tty: function() { return write('tty'); },\n    _statwatcher: function() { return write('statwatcher'); },\n    _securecontext: function() { return write('securecontext'); },\n    _connection: function() { return write('connection'); },\n    _zlib: function() { return write('zlib'); },\n    _context: function() { return write('context'); },\n    _nodescript: function() { return write('nodescript'); },\n    _httpparser: function() { return write('httpparser'); },\n    _dataview: function() { return write('dataview'); },\n    _signal: function() { return write('signal'); },\n    _fsevent: function() { return write('fsevent'); },\n    _tlswrap: function() { return write('tlswrap'); },\n  };\n}\n\n// Mini-implementation of stream.PassThrough\n// We are far from having need for the full implementation, and we can\n// make assumptions like \"many writes, then only one final read\"\n// and we can ignore encoding specifics\nfunction PassThrough() {\n  return {\n    buf: '',\n\n    write: function(b) {\n      this.buf += b;\n    },\n\n    end: function(b) {\n      this.buf += b;\n    },\n\n    read: function() {\n      return this.buf;\n    }\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/openid-client/node_modules/object-hash/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/openid-client/package.json":
/*!*************************************************!*\
  !*** ./node_modules/openid-client/package.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"openid-client","version":"5.7.0","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/node-openid-client","repository":"panva/node-openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}');

/***/ })

};
;