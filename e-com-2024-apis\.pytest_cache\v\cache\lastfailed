{"tests/test_api_optimization.py::TestAPIResponseOptimization::test_gzip_compression": true, "tests/test_api_optimization.py::TestAPIResponseOptimization::test_field_filtering": true, "tests/test_api_optimization.py::TestAPIResponseOptimization::test_pagination": true, "tests/test_optimizations.py::TestCaching::test_featured_products_caching": true, "tests/test_optimizations.py::TestCaching::test_active_categories_caching": true, "tests/test_optimizations.py::TestDatabaseOptimization::test_product_list_query_optimization": true, "tests/test_optimizations.py::TestDatabaseOptimization::test_order_detail_query_optimization": true, "tests/test_transaction_management.py::TestTransactionManagement::test_order_creation_transaction": true, "tests/test_transaction_management.py::TestTransactionManagement::test_order_cancellation_transaction": true, "tests/test_transaction_management.py::TestTransactionManagement::test_optimistic_concurrency_control": true}