"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/formik";
exports.ids = ["vendor-chunks/formik"];
exports.modules = {

/***/ "(ssr)/./node_modules/formik/dist/formik.esm.js":
/*!************************************************!*\
  !*** ./node_modules/formik/dist/formik.esm.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorMessage: () => (/* binding */ ErrorMessage),\n/* harmony export */   FastField: () => (/* binding */ FastField),\n/* harmony export */   Field: () => (/* binding */ Field),\n/* harmony export */   FieldArray: () => (/* binding */ FieldArray),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   Formik: () => (/* binding */ Formik),\n/* harmony export */   FormikConsumer: () => (/* binding */ FormikConsumer),\n/* harmony export */   FormikContext: () => (/* binding */ FormikContext),\n/* harmony export */   FormikProvider: () => (/* binding */ FormikProvider),\n/* harmony export */   connect: () => (/* binding */ connect),\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement),\n/* harmony export */   getIn: () => (/* binding */ getIn),\n/* harmony export */   insert: () => (/* binding */ insert),\n/* harmony export */   isEmptyArray: () => (/* binding */ isEmptyArray),\n/* harmony export */   isEmptyChildren: () => (/* binding */ isEmptyChildren),\n/* harmony export */   isFunction: () => (/* binding */ isFunction),\n/* harmony export */   isInputEvent: () => (/* binding */ isInputEvent),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN$1),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   move: () => (/* binding */ move),\n/* harmony export */   prepareDataForValidation: () => (/* binding */ prepareDataForValidation),\n/* harmony export */   replace: () => (/* binding */ replace),\n/* harmony export */   setIn: () => (/* binding */ setIn),\n/* harmony export */   setNestedObjectValues: () => (/* binding */ setNestedObjectValues),\n/* harmony export */   swap: () => (/* binding */ swap),\n/* harmony export */   useField: () => (/* binding */ useField),\n/* harmony export */   useFormik: () => (/* binding */ useFormik),\n/* harmony export */   useFormikContext: () => (/* binding */ useFormikContext),\n/* harmony export */   validateYupSchema: () => (/* binding */ validateYupSchema),\n/* harmony export */   withFormik: () => (/* binding */ withFormik),\n/* harmony export */   yupToFormErrors: () => (/* binding */ yupToFormErrors)\n/* harmony export */ });\n/* harmony import */ var deepmerge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! deepmerge */ \"(ssr)/./node_modules/deepmerge/dist/es.js\");\n/* harmony import */ var lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lodash-es/isPlainObject */ \"(ssr)/./node_modules/lodash-es/isPlainObject.js\");\n/* harmony import */ var lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lodash-es/cloneDeep */ \"(ssr)/./node_modules/lodash-es/cloneDeep.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-fast-compare */ \"(ssr)/./node_modules/react-fast-compare/index.js\");\n/* harmony import */ var react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_fast_compare__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tiny_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tiny-warning */ \"(ssr)/./node_modules/tiny-warning/dist/tiny-warning.esm.js\");\n/* harmony import */ var lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es/clone */ \"(ssr)/./node_modules/lodash-es/clone.js\");\n/* harmony import */ var lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es/toPath */ \"(ssr)/./node_modules/lodash-es/toPath.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hoist-non-react-statics */ \"(ssr)/./node_modules/hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js\");\n/* harmony import */ var hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n\n\n\n\n\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nvar FormikContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nFormikContext.displayName = 'FormikContext';\nvar FormikProvider = FormikContext.Provider;\nvar FormikConsumer = FormikContext.Consumer;\nfunction useFormikContext() {\n  var formik = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(FormikContext);\n  !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component.\") : 0 : void 0;\n  return formik;\n}\n\n/** @private is the value an empty array? */\n\nvar isEmptyArray = function isEmptyArray(value) {\n  return Array.isArray(value) && value.length === 0;\n};\n/** @private is the given object a Function? */\n\nvar isFunction = function isFunction(obj) {\n  return typeof obj === 'function';\n};\n/** @private is the given object an Object? */\n\nvar isObject = function isObject(obj) {\n  return obj !== null && typeof obj === 'object';\n};\n/** @private is the given object an integer? */\n\nvar isInteger = function isInteger(obj) {\n  return String(Math.floor(Number(obj))) === obj;\n};\n/** @private is the given object a string? */\n\nvar isString = function isString(obj) {\n  return Object.prototype.toString.call(obj) === '[object String]';\n};\n/** @private is the given object a NaN? */\n// eslint-disable-next-line no-self-compare\n\nvar isNaN$1 = function isNaN(obj) {\n  return obj !== obj;\n};\n/** @private Does a React component have exactly 0 children? */\n\nvar isEmptyChildren = function isEmptyChildren(children) {\n  return react__WEBPACK_IMPORTED_MODULE_1__.Children.count(children) === 0;\n};\n/** @private is the given object/value a promise? */\n\nvar isPromise = function isPromise(value) {\n  return isObject(value) && isFunction(value.then);\n};\n/** @private is the given object/value a type of synthetic event? */\n\nvar isInputEvent = function isInputEvent(value) {\n  return value && isObject(value) && isObject(value.target);\n};\n/**\r\n * Same as document.activeElement but wraps in a try-catch block. In IE it is\r\n * not safe to call document.activeElement if there is nothing focused.\r\n *\r\n * The activeElement will be null only if the document or document body is not\r\n * yet defined.\r\n *\r\n * @param {?Document} doc Defaults to current document.\r\n * @return {Element | null}\r\n * @see https://github.com/facebook/fbjs/blob/master/packages/fbjs/src/core/dom/getActiveElement.js\r\n */\n\nfunction getActiveElement(doc) {\n  doc = doc || (typeof document !== 'undefined' ? document : undefined);\n\n  if (typeof doc === 'undefined') {\n    return null;\n  }\n\n  try {\n    return doc.activeElement || doc.body;\n  } catch (e) {\n    return doc.body;\n  }\n}\n/**\r\n * Deeply get a value from an object via its path.\r\n */\n\nfunction getIn(obj, key, def, p) {\n  if (p === void 0) {\n    p = 0;\n  }\n\n  var path = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key);\n\n  while (obj && p < path.length) {\n    obj = obj[path[p++]];\n  } // check if path is not in the end\n\n\n  if (p !== path.length && !obj) {\n    return def;\n  }\n\n  return obj === undefined ? def : obj;\n}\n/**\r\n * Deeply set a value from in object via it's path. If the value at `path`\r\n * has changed, return a shallow copy of obj with `value` set at `path`.\r\n * If `value` has not changed, return the original `obj`.\r\n *\r\n * Existing objects / arrays along `path` are also shallow copied. Sibling\r\n * objects along path retain the same internal js reference. Since new\r\n * objects / arrays are only created along `path`, we can test if anything\r\n * changed in a nested structure by comparing the object's reference in\r\n * the old and new object, similar to how russian doll cache invalidation\r\n * works.\r\n *\r\n * In earlier versions of this function, which used cloneDeep, there were\r\n * issues whereby settings a nested value would mutate the parent\r\n * instead of creating a new object. `clone` avoids that bug making a\r\n * shallow copy of the objects along the update path\r\n * so no object is mutated in place.\r\n *\r\n * Before changing this function, please read through the following\r\n * discussions.\r\n *\r\n * @see https://github.com/developit/linkstate\r\n * @see https://github.com/jaredpalmer/formik/pull/123\r\n */\n\nfunction setIn(obj, path, value) {\n  var res = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(obj); // this keeps inheritance when obj is a class\n\n  var resVal = res;\n  var i = 0;\n  var pathArray = (0,lodash_es_toPath__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(path);\n\n  for (; i < pathArray.length - 1; i++) {\n    var currentPath = pathArray[i];\n    var currentObj = getIn(obj, pathArray.slice(0, i + 1));\n\n    if (currentObj && (isObject(currentObj) || Array.isArray(currentObj))) {\n      resVal = resVal[currentPath] = (0,lodash_es_clone__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(currentObj);\n    } else {\n      var nextPath = pathArray[i + 1];\n      resVal = resVal[currentPath] = isInteger(nextPath) && Number(nextPath) >= 0 ? [] : {};\n    }\n  } // Return original object if new value is the same as current\n\n\n  if ((i === 0 ? obj : resVal)[pathArray[i]] === value) {\n    return obj;\n  }\n\n  if (value === undefined) {\n    delete resVal[pathArray[i]];\n  } else {\n    resVal[pathArray[i]] = value;\n  } // If the path array has a single element, the loop did not run.\n  // Deleting on `resVal` had no effect in this scenario, so we delete on the result instead.\n\n\n  if (i === 0 && value === undefined) {\n    delete res[pathArray[i]];\n  }\n\n  return res;\n}\n/**\r\n * Recursively a set the same value for all keys and arrays nested object, cloning\r\n * @param object\r\n * @param value\r\n * @param visited\r\n * @param response\r\n */\n\nfunction setNestedObjectValues(object, value, visited, response) {\n  if (visited === void 0) {\n    visited = new WeakMap();\n  }\n\n  if (response === void 0) {\n    response = {};\n  }\n\n  for (var _i = 0, _Object$keys = Object.keys(object); _i < _Object$keys.length; _i++) {\n    var k = _Object$keys[_i];\n    var val = object[k];\n\n    if (isObject(val)) {\n      if (!visited.get(val)) {\n        visited.set(val, true); // In order to keep array values consistent for both dot path  and\n        // bracket syntax, we need to check if this is an array so that\n        // this will output  { friends: [true] } and not { friends: { \"0\": true } }\n\n        response[k] = Array.isArray(val) ? [] : {};\n        setNestedObjectValues(val, value, visited, response[k]);\n      }\n    } else {\n      response[k] = value;\n    }\n  }\n\n  return response;\n}\n\nfunction formikReducer(state, msg) {\n  switch (msg.type) {\n    case 'SET_VALUES':\n      return _extends({}, state, {\n        values: msg.payload\n      });\n\n    case 'SET_TOUCHED':\n      return _extends({}, state, {\n        touched: msg.payload\n      });\n\n    case 'SET_ERRORS':\n      if (react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(state.errors, msg.payload)) {\n        return state;\n      }\n\n      return _extends({}, state, {\n        errors: msg.payload\n      });\n\n    case 'SET_STATUS':\n      return _extends({}, state, {\n        status: msg.payload\n      });\n\n    case 'SET_ISSUBMITTING':\n      return _extends({}, state, {\n        isSubmitting: msg.payload\n      });\n\n    case 'SET_ISVALIDATING':\n      return _extends({}, state, {\n        isValidating: msg.payload\n      });\n\n    case 'SET_FIELD_VALUE':\n      return _extends({}, state, {\n        values: setIn(state.values, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_TOUCHED':\n      return _extends({}, state, {\n        touched: setIn(state.touched, msg.payload.field, msg.payload.value)\n      });\n\n    case 'SET_FIELD_ERROR':\n      return _extends({}, state, {\n        errors: setIn(state.errors, msg.payload.field, msg.payload.value)\n      });\n\n    case 'RESET_FORM':\n      return _extends({}, state, msg.payload);\n\n    case 'SET_FORMIK_STATE':\n      return msg.payload(state);\n\n    case 'SUBMIT_ATTEMPT':\n      return _extends({}, state, {\n        touched: setNestedObjectValues(state.values, true),\n        isSubmitting: true,\n        submitCount: state.submitCount + 1\n      });\n\n    case 'SUBMIT_FAILURE':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    case 'SUBMIT_SUCCESS':\n      return _extends({}, state, {\n        isSubmitting: false\n      });\n\n    default:\n      return state;\n  }\n} // Initial empty states // objects\n\n\nvar emptyErrors = {};\nvar emptyTouched = {};\nfunction useFormik(_ref) {\n  var _ref$validateOnChange = _ref.validateOnChange,\n      validateOnChange = _ref$validateOnChange === void 0 ? true : _ref$validateOnChange,\n      _ref$validateOnBlur = _ref.validateOnBlur,\n      validateOnBlur = _ref$validateOnBlur === void 0 ? true : _ref$validateOnBlur,\n      _ref$validateOnMount = _ref.validateOnMount,\n      validateOnMount = _ref$validateOnMount === void 0 ? false : _ref$validateOnMount,\n      isInitialValid = _ref.isInitialValid,\n      _ref$enableReinitiali = _ref.enableReinitialize,\n      enableReinitialize = _ref$enableReinitiali === void 0 ? false : _ref$enableReinitiali,\n      onSubmit = _ref.onSubmit,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"validateOnChange\", \"validateOnBlur\", \"validateOnMount\", \"isInitialValid\", \"enableReinitialize\", \"onSubmit\"]);\n\n  var props = _extends({\n    validateOnChange: validateOnChange,\n    validateOnBlur: validateOnBlur,\n    validateOnMount: validateOnMount,\n    onSubmit: onSubmit\n  }, rest);\n\n  var initialValues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialValues);\n  var initialErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialErrors || emptyErrors);\n  var initialTouched = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialTouched || emptyTouched);\n  var initialStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(props.initialStatus);\n  var isMounted = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var fieldRegistry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !(typeof isInitialValid === 'undefined') ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n\n  var _React$useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),\n      setIteration = _React$useState[1];\n\n  var stateRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n    values: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialValues),\n    errors: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialErrors) || emptyErrors,\n    touched: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialTouched) || emptyTouched,\n    status: (0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(props.initialStatus),\n    isSubmitting: false,\n    isValidating: false,\n    submitCount: 0\n  });\n  var state = stateRef.current;\n  var dispatch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (action) {\n    var prev = stateRef.current;\n    stateRef.current = formikReducer(prev, action); // force rerender\n\n    if (prev !== stateRef.current) setIteration(function (x) {\n      return x + 1;\n    });\n  }, []);\n  var runValidateHandler = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    return new Promise(function (resolve, reject) {\n      var maybePromisedErrors = props.validate(values, field);\n\n      if (maybePromisedErrors == null) {\n        // use loose null check here on purpose\n        resolve(emptyErrors);\n      } else if (isPromise(maybePromisedErrors)) {\n        maybePromisedErrors.then(function (errors) {\n          resolve(errors || emptyErrors);\n        }, function (actualException) {\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validate />\", actualException);\n          }\n\n          reject(actualException);\n        });\n      } else {\n        resolve(maybePromisedErrors);\n      }\n    });\n  }, [props.validate]);\n  /**\r\n   * Run validation against a Yup schema and optionally run a function if successful\r\n   */\n\n  var runValidationSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values, field) {\n    var validationSchema = props.validationSchema;\n    var schema = isFunction(validationSchema) ? validationSchema(field) : validationSchema;\n    var promise = field && schema.validateAt ? schema.validateAt(field, values) : validateYupSchema(values, schema);\n    return new Promise(function (resolve, reject) {\n      promise.then(function () {\n        resolve(emptyErrors);\n      }, function (err) {\n        // Yup will throw a validation error if validation fails. We catch those and\n        // resolve them into Formik errors. We can sniff if something is a Yup error\n        // by checking error.name.\n        // @see https://github.com/jquense/yup#validationerrorerrors-string--arraystring-value-any-path-string\n        if (err.name === 'ValidationError') {\n          resolve(yupToFormErrors(err));\n        } else {\n          // We throw any other errors\n          if (true) {\n            console.warn(\"Warning: An unhandled error was caught during validation in <Formik validationSchema />\", err);\n          }\n\n          reject(err);\n        }\n      });\n    });\n  }, [props.validationSchema]);\n  var runSingleFieldLevelValidation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    return new Promise(function (resolve) {\n      return resolve(fieldRegistry.current[field].validate(value));\n    });\n  }, []);\n  var runFieldLevelValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    var fieldKeysWithValidation = Object.keys(fieldRegistry.current).filter(function (f) {\n      return isFunction(fieldRegistry.current[f].validate);\n    }); // Construct an array with all of the field validation functions\n\n    var fieldValidations = fieldKeysWithValidation.length > 0 ? fieldKeysWithValidation.map(function (f) {\n      return runSingleFieldLevelValidation(f, getIn(values, f));\n    }) : [Promise.resolve('DO_NOT_DELETE_YOU_WILL_BE_FIRED')]; // use special case ;)\n\n    return Promise.all(fieldValidations).then(function (fieldErrorsList) {\n      return fieldErrorsList.reduce(function (prev, curr, index) {\n        if (curr === 'DO_NOT_DELETE_YOU_WILL_BE_FIRED') {\n          return prev;\n        }\n\n        if (curr) {\n          prev = setIn(prev, fieldKeysWithValidation[index], curr);\n        }\n\n        return prev;\n      }, {});\n    });\n  }, [runSingleFieldLevelValidation]); // Run all validations and return the result\n\n  var runAllValidations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (values) {\n    return Promise.all([runFieldLevelValidations(values), props.validationSchema ? runValidationSchema(values) : {}, props.validate ? runValidateHandler(values) : {}]).then(function (_ref2) {\n      var fieldErrors = _ref2[0],\n          schemaErrors = _ref2[1],\n          validateErrors = _ref2[2];\n      var combinedErrors = deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"].all([fieldErrors, schemaErrors, validateErrors], {\n        arrayMerge: arrayMerge\n      });\n      return combinedErrors;\n    });\n  }, [props.validate, props.validationSchema, runFieldLevelValidations, runValidateHandler, runValidationSchema]); // Run all validations methods and update state accordingly\n\n  var validateFormWithHighPriority = useEventCallback(function (values) {\n    if (values === void 0) {\n      values = state.values;\n    }\n\n    dispatch({\n      type: 'SET_ISVALIDATING',\n      payload: true\n    });\n    return runAllValidations(values).then(function (combinedErrors) {\n      if (!!isMounted.current) {\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n        dispatch({\n          type: 'SET_ERRORS',\n          payload: combinedErrors\n        });\n      }\n\n      return combinedErrors;\n    });\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (validateOnMount && isMounted.current === true && react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      validateFormWithHighPriority(initialValues.current);\n    }\n  }, [validateOnMount, validateFormWithHighPriority]);\n  var resetForm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nextState) {\n    var values = nextState && nextState.values ? nextState.values : initialValues.current;\n    var errors = nextState && nextState.errors ? nextState.errors : initialErrors.current ? initialErrors.current : props.initialErrors || {};\n    var touched = nextState && nextState.touched ? nextState.touched : initialTouched.current ? initialTouched.current : props.initialTouched || {};\n    var status = nextState && nextState.status ? nextState.status : initialStatus.current ? initialStatus.current : props.initialStatus;\n    initialValues.current = values;\n    initialErrors.current = errors;\n    initialTouched.current = touched;\n    initialStatus.current = status;\n\n    var dispatchFn = function dispatchFn() {\n      dispatch({\n        type: 'RESET_FORM',\n        payload: {\n          isSubmitting: !!nextState && !!nextState.isSubmitting,\n          errors: errors,\n          touched: touched,\n          status: status,\n          values: values,\n          isValidating: !!nextState && !!nextState.isValidating,\n          submitCount: !!nextState && !!nextState.submitCount && typeof nextState.submitCount === 'number' ? nextState.submitCount : 0\n        }\n      });\n    };\n\n    if (props.onReset) {\n      var maybePromisedOnReset = props.onReset(state.values, imperativeMethods);\n\n      if (isPromise(maybePromisedOnReset)) {\n        maybePromisedOnReset.then(dispatchFn);\n      } else {\n        dispatchFn();\n      }\n    } else {\n      dispatchFn();\n    }\n  }, [props.initialErrors, props.initialStatus, props.initialTouched, props.onReset]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, props.initialValues)) {\n      if (enableReinitialize) {\n        initialValues.current = props.initialValues;\n        resetForm();\n\n        if (validateOnMount) {\n          validateFormWithHighPriority(initialValues.current);\n        }\n      }\n    }\n  }, [enableReinitialize, props.initialValues, resetForm, validateOnMount, validateFormWithHighPriority]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialErrors.current, props.initialErrors)) {\n      initialErrors.current = props.initialErrors || emptyErrors;\n      dispatch({\n        type: 'SET_ERRORS',\n        payload: props.initialErrors || emptyErrors\n      });\n    }\n  }, [enableReinitialize, props.initialErrors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialTouched.current, props.initialTouched)) {\n      initialTouched.current = props.initialTouched || emptyTouched;\n      dispatch({\n        type: 'SET_TOUCHED',\n        payload: props.initialTouched || emptyTouched\n      });\n    }\n  }, [enableReinitialize, props.initialTouched]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (enableReinitialize && isMounted.current === true && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialStatus.current, props.initialStatus)) {\n      initialStatus.current = props.initialStatus;\n      dispatch({\n        type: 'SET_STATUS',\n        payload: props.initialStatus\n      });\n    }\n  }, [enableReinitialize, props.initialStatus, props.initialTouched]);\n  var validateField = useEventCallback(function (name) {\n    // This will efficiently validate a single field by avoiding state\n    // changes if the validation function is synchronous. It's different from\n    // what is called when using validateForm.\n    if (fieldRegistry.current[name] && isFunction(fieldRegistry.current[name].validate)) {\n      var value = getIn(state.values, name);\n      var maybePromise = fieldRegistry.current[name].validate(value);\n\n      if (isPromise(maybePromise)) {\n        // Only flip isValidating if the function is async.\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: true\n        });\n        return maybePromise.then(function (x) {\n          return x;\n        }).then(function (error) {\n          dispatch({\n            type: 'SET_FIELD_ERROR',\n            payload: {\n              field: name,\n              value: error\n            }\n          });\n          dispatch({\n            type: 'SET_ISVALIDATING',\n            payload: false\n          });\n        });\n      } else {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: maybePromise\n          }\n        });\n        return Promise.resolve(maybePromise);\n      }\n    } else if (props.validationSchema) {\n      dispatch({\n        type: 'SET_ISVALIDATING',\n        payload: true\n      });\n      return runValidationSchema(state.values, name).then(function (x) {\n        return x;\n      }).then(function (error) {\n        dispatch({\n          type: 'SET_FIELD_ERROR',\n          payload: {\n            field: name,\n            value: getIn(error, name)\n          }\n        });\n        dispatch({\n          type: 'SET_ISVALIDATING',\n          payload: false\n        });\n      });\n    }\n\n    return Promise.resolve();\n  });\n  var registerField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name, _ref3) {\n    var validate = _ref3.validate;\n    fieldRegistry.current[name] = {\n      validate: validate\n    };\n  }, []);\n  var unregisterField = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    delete fieldRegistry.current[name];\n  }, []);\n  var setTouched = useEventCallback(function (touched, shouldValidate) {\n    dispatch({\n      type: 'SET_TOUCHED',\n      payload: touched\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var setErrors = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (errors) {\n    dispatch({\n      type: 'SET_ERRORS',\n      payload: errors\n    });\n  }, []);\n  var setValues = useEventCallback(function (values, shouldValidate) {\n    var resolvedValues = isFunction(values) ? values(state.values) : values;\n    dispatch({\n      type: 'SET_VALUES',\n      payload: resolvedValues\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(resolvedValues) : Promise.resolve();\n  });\n  var setFieldError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (field, value) {\n    dispatch({\n      type: 'SET_FIELD_ERROR',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n  }, []);\n  var setFieldValue = useEventCallback(function (field, value, shouldValidate) {\n    dispatch({\n      type: 'SET_FIELD_VALUE',\n      payload: {\n        field: field,\n        value: value\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnChange : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(setIn(state.values, field, value)) : Promise.resolve();\n  });\n  var executeChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (eventOrTextValue, maybePath) {\n    // By default, assume that the first argument is a string. This allows us to use\n    // handleChange with React Native and React Native Web's onChangeText prop which\n    // provides just the value of the input.\n    var field = maybePath;\n    var val = eventOrTextValue;\n    var parsed; // If the first argument is not a string though, it has to be a synthetic React Event (or a fake one),\n    // so we handle like we would a normal HTML change event.\n\n    if (!isString(eventOrTextValue)) {\n      // If we can, persist the event\n      // @see https://reactjs.org/docs/events.html#event-pooling\n      if (eventOrTextValue.persist) {\n        eventOrTextValue.persist();\n      }\n\n      var target = eventOrTextValue.target ? eventOrTextValue.target : eventOrTextValue.currentTarget;\n      var type = target.type,\n          name = target.name,\n          id = target.id,\n          value = target.value,\n          checked = target.checked,\n          outerHTML = target.outerHTML,\n          options = target.options,\n          multiple = target.multiple;\n      field = maybePath ? maybePath : name ? name : id;\n\n      if (!field && \"development\" !== \"production\") {\n        warnAboutMissingIdentifier({\n          htmlContent: outerHTML,\n          documentationAnchorLink: 'handlechange-e-reactchangeeventany--void',\n          handlerName: 'handleChange'\n        });\n      }\n\n      val = /number|range/.test(type) ? (parsed = parseFloat(value), isNaN(parsed) ? '' : parsed) : /checkbox/.test(type) // checkboxes\n      ? getValueForCheckbox(getIn(state.values, field), checked, value) : options && multiple // <select multiple>\n      ? getSelectedValues(options) : value;\n    }\n\n    if (field) {\n      // Set form fields by name\n      setFieldValue(field, val);\n    }\n  }, [setFieldValue, state.values]);\n  var handleChange = useEventCallback(function (eventOrPath) {\n    if (isString(eventOrPath)) {\n      return function (event) {\n        return executeChange(event, eventOrPath);\n      };\n    } else {\n      executeChange(eventOrPath);\n    }\n  });\n  var setFieldTouched = useEventCallback(function (field, touched, shouldValidate) {\n    if (touched === void 0) {\n      touched = true;\n    }\n\n    dispatch({\n      type: 'SET_FIELD_TOUCHED',\n      payload: {\n        field: field,\n        value: touched\n      }\n    });\n    var willValidate = shouldValidate === undefined ? validateOnBlur : shouldValidate;\n    return willValidate ? validateFormWithHighPriority(state.values) : Promise.resolve();\n  });\n  var executeBlur = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (e, path) {\n    if (e.persist) {\n      e.persist();\n    }\n\n    var _e$target = e.target,\n        name = _e$target.name,\n        id = _e$target.id,\n        outerHTML = _e$target.outerHTML;\n    var field = path ? path : name ? name : id;\n\n    if (!field && \"development\" !== \"production\") {\n      warnAboutMissingIdentifier({\n        htmlContent: outerHTML,\n        documentationAnchorLink: 'handleblur-e-any--void',\n        handlerName: 'handleBlur'\n      });\n    }\n\n    setFieldTouched(field, true);\n  }, [setFieldTouched]);\n  var handleBlur = useEventCallback(function (eventOrString) {\n    if (isString(eventOrString)) {\n      return function (event) {\n        return executeBlur(event, eventOrString);\n      };\n    } else {\n      executeBlur(eventOrString);\n    }\n  });\n  var setFormikState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (stateOrCb) {\n    if (isFunction(stateOrCb)) {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: stateOrCb\n      });\n    } else {\n      dispatch({\n        type: 'SET_FORMIK_STATE',\n        payload: function payload() {\n          return stateOrCb;\n        }\n      });\n    }\n  }, []);\n  var setStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (status) {\n    dispatch({\n      type: 'SET_STATUS',\n      payload: status\n    });\n  }, []);\n  var setSubmitting = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (isSubmitting) {\n    dispatch({\n      type: 'SET_ISSUBMITTING',\n      payload: isSubmitting\n    });\n  }, []);\n  var submitForm = useEventCallback(function () {\n    dispatch({\n      type: 'SUBMIT_ATTEMPT'\n    });\n    return validateFormWithHighPriority().then(function (combinedErrors) {\n      // In case an error was thrown and passed to the resolved Promise,\n      // `combinedErrors` can be an instance of an Error. We need to check\n      // that and abort the submit.\n      // If we don't do that, calling `Object.keys(new Error())` yields an\n      // empty array, which causes the validation to pass and the form\n      // to be submitted.\n      var isInstanceOfError = combinedErrors instanceof Error;\n      var isActuallyValid = !isInstanceOfError && Object.keys(combinedErrors).length === 0;\n\n      if (isActuallyValid) {\n        // Proceed with submit...\n        //\n        // To respect sync submit fns, we can't simply wrap executeSubmit in a promise and\n        // _always_ dispatch SUBMIT_SUCCESS because isSubmitting would then always be false.\n        // This would be fine in simple cases, but make it impossible to disable submit\n        // buttons where people use callbacks or promises as side effects (which is basically\n        // all of v1 Formik code). Instead, recall that we are inside of a promise chain already,\n        //  so we can try/catch executeSubmit(), if it returns undefined, then just bail.\n        // If there are errors, throw em. Otherwise, wrap executeSubmit in a promise and handle\n        // cleanup of isSubmitting on behalf of the consumer.\n        var promiseOrUndefined;\n\n        try {\n          promiseOrUndefined = executeSubmit(); // Bail if it's sync, consumer is responsible for cleaning up\n          // via setSubmitting(false)\n\n          if (promiseOrUndefined === undefined) {\n            return;\n          }\n        } catch (error) {\n          throw error;\n        }\n\n        return Promise.resolve(promiseOrUndefined).then(function (result) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_SUCCESS'\n            });\n          }\n\n          return result;\n        })[\"catch\"](function (_errors) {\n          if (!!isMounted.current) {\n            dispatch({\n              type: 'SUBMIT_FAILURE'\n            }); // This is a legit error rejected by the onSubmit fn\n            // so we don't want to break the promise chain\n\n            throw _errors;\n          }\n        });\n      } else if (!!isMounted.current) {\n        // ^^^ Make sure Formik is still mounted before updating state\n        dispatch({\n          type: 'SUBMIT_FAILURE'\n        }); // throw combinedErrors;\n\n        if (isInstanceOfError) {\n          throw combinedErrors;\n        }\n      }\n\n      return;\n    });\n  });\n  var handleSubmit = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    } // Warn if form submission is triggered by a <button> without a\n    // specified `type` attribute during development. This mitigates\n    // a common gotcha in forms with both reset and submit buttons,\n    // where the dev forgets to add type=\"button\" to the reset button.\n\n\n    if ( true && typeof document !== 'undefined') {\n      // Safely get the active element (works with IE)\n      var activeElement = getActiveElement();\n\n      if (activeElement !== null && activeElement instanceof HTMLButtonElement) {\n        !(activeElement.attributes && activeElement.attributes.getNamedItem('type')) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type=\"submit\"`. If this is not a submit button, please add `type=\"button\"`.') : 0 : void 0;\n      }\n    }\n\n    submitForm()[\"catch\"](function (reason) {\n      console.warn(\"Warning: An unhandled error was caught from submitForm()\", reason);\n    });\n  });\n  var imperativeMethods = {\n    resetForm: resetForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    setErrors: setErrors,\n    setFieldError: setFieldError,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    setFormikState: setFormikState,\n    submitForm: submitForm\n  };\n  var executeSubmit = useEventCallback(function () {\n    return onSubmit(state.values, imperativeMethods);\n  });\n  var handleReset = useEventCallback(function (e) {\n    if (e && e.preventDefault && isFunction(e.preventDefault)) {\n      e.preventDefault();\n    }\n\n    if (e && e.stopPropagation && isFunction(e.stopPropagation)) {\n      e.stopPropagation();\n    }\n\n    resetForm();\n  });\n  var getFieldMeta = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      value: getIn(state.values, name),\n      error: getIn(state.errors, name),\n      touched: !!getIn(state.touched, name),\n      initialValue: getIn(initialValues.current, name),\n      initialTouched: !!getIn(initialTouched.current, name),\n      initialError: getIn(initialErrors.current, name)\n    };\n  }, [state.errors, state.touched, state.values]);\n  var getFieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (name) {\n    return {\n      setValue: function setValue(value, shouldValidate) {\n        return setFieldValue(name, value, shouldValidate);\n      },\n      setTouched: function setTouched(value, shouldValidate) {\n        return setFieldTouched(name, value, shouldValidate);\n      },\n      setError: function setError(value) {\n        return setFieldError(name, value);\n      }\n    };\n  }, [setFieldValue, setFieldTouched, setFieldError]);\n  var getFieldProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (nameOrOptions) {\n    var isAnObject = isObject(nameOrOptions);\n    var name = isAnObject ? nameOrOptions.name : nameOrOptions;\n    var valueState = getIn(state.values, name);\n    var field = {\n      name: name,\n      value: valueState,\n      onChange: handleChange,\n      onBlur: handleBlur\n    };\n\n    if (isAnObject) {\n      var type = nameOrOptions.type,\n          valueProp = nameOrOptions.value,\n          is = nameOrOptions.as,\n          multiple = nameOrOptions.multiple;\n\n      if (type === 'checkbox') {\n        if (valueProp === undefined) {\n          field.checked = !!valueState;\n        } else {\n          field.checked = !!(Array.isArray(valueState) && ~valueState.indexOf(valueProp));\n          field.value = valueProp;\n        }\n      } else if (type === 'radio') {\n        field.checked = valueState === valueProp;\n        field.value = valueProp;\n      } else if (is === 'select' && multiple) {\n        field.value = field.value || [];\n        field.multiple = true;\n      }\n    }\n\n    return field;\n  }, [handleBlur, handleChange, state.values]);\n  var dirty = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(initialValues.current, state.values);\n  }, [initialValues.current, state.values]);\n  var isValid = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return typeof isInitialValid !== 'undefined' ? dirty ? state.errors && Object.keys(state.errors).length === 0 : isInitialValid !== false && isFunction(isInitialValid) ? isInitialValid(props) : isInitialValid : state.errors && Object.keys(state.errors).length === 0;\n  }, [isInitialValid, dirty, state.errors, props]);\n\n  var ctx = _extends({}, state, {\n    initialValues: initialValues.current,\n    initialErrors: initialErrors.current,\n    initialTouched: initialTouched.current,\n    initialStatus: initialStatus.current,\n    handleBlur: handleBlur,\n    handleChange: handleChange,\n    handleReset: handleReset,\n    handleSubmit: handleSubmit,\n    resetForm: resetForm,\n    setErrors: setErrors,\n    setFormikState: setFormikState,\n    setFieldTouched: setFieldTouched,\n    setFieldValue: setFieldValue,\n    setFieldError: setFieldError,\n    setStatus: setStatus,\n    setSubmitting: setSubmitting,\n    setTouched: setTouched,\n    setValues: setValues,\n    submitForm: submitForm,\n    validateForm: validateFormWithHighPriority,\n    validateField: validateField,\n    isValid: isValid,\n    dirty: dirty,\n    unregisterField: unregisterField,\n    registerField: registerField,\n    getFieldProps: getFieldProps,\n    getFieldMeta: getFieldMeta,\n    getFieldHelpers: getFieldHelpers,\n    validateOnBlur: validateOnBlur,\n    validateOnChange: validateOnChange,\n    validateOnMount: validateOnMount\n  });\n\n  return ctx;\n}\nfunction Formik(props) {\n  var formikbag = useFormik(props);\n  var component = props.component,\n      children = props.children,\n      render = props.render,\n      innerRef = props.innerRef; // This allows folks to pass a ref to <Formik />\n\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(innerRef, function () {\n    return formikbag;\n  });\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!props.render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>\") : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikProvider, {\n    value: formikbag\n  }, component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, formikbag) : render ? render(formikbag) : children // children come last, always called\n  ? isFunction(children) ? children(formikbag) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null);\n}\n\nfunction warnAboutMissingIdentifier(_ref4) {\n  var htmlContent = _ref4.htmlContent,\n      documentationAnchorLink = _ref4.documentationAnchorLink,\n      handlerName = _ref4.handlerName;\n  console.warn(\"Warning: Formik called `\" + handlerName + \"`, but you forgot to pass an `id` or `name` attribute to your input:\\n    \" + htmlContent + \"\\n    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#\" + documentationAnchorLink + \"\\n  \");\n}\n/**\r\n * Transform Yup ValidationError to a more usable object\r\n */\n\n\nfunction yupToFormErrors(yupError) {\n  var errors = {};\n\n  if (yupError.inner) {\n    if (yupError.inner.length === 0) {\n      return setIn(errors, yupError.path, yupError.message);\n    }\n\n    for (var _iterator = yupError.inner, _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {\n      var _ref5;\n\n      if (_isArray) {\n        if (_i >= _iterator.length) break;\n        _ref5 = _iterator[_i++];\n      } else {\n        _i = _iterator.next();\n        if (_i.done) break;\n        _ref5 = _i.value;\n      }\n\n      var err = _ref5;\n\n      if (!getIn(errors, err.path)) {\n        errors = setIn(errors, err.path, err.message);\n      }\n    }\n  }\n\n  return errors;\n}\n/**\r\n * Validate a yup schema.\r\n */\n\nfunction validateYupSchema(values, schema, sync, context) {\n  if (sync === void 0) {\n    sync = false;\n  }\n\n  var normalizedValues = prepareDataForValidation(values);\n  return schema[sync ? 'validateSync' : 'validate'](normalizedValues, {\n    abortEarly: false,\n    context: context || normalizedValues\n  });\n}\n/**\r\n * Recursively prepare values.\r\n */\n\nfunction prepareDataForValidation(values) {\n  var data = Array.isArray(values) ? [] : {};\n\n  for (var k in values) {\n    if (Object.prototype.hasOwnProperty.call(values, k)) {\n      var key = String(k);\n\n      if (Array.isArray(values[key]) === true) {\n        data[key] = values[key].map(function (value) {\n          if (Array.isArray(value) === true || (0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(value)) {\n            return prepareDataForValidation(value);\n          } else {\n            return value !== '' ? value : undefined;\n          }\n        });\n      } else if ((0,lodash_es_isPlainObject__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(values[key])) {\n        data[key] = prepareDataForValidation(values[key]);\n      } else {\n        data[key] = values[key] !== '' ? values[key] : undefined;\n      }\n    }\n  }\n\n  return data;\n}\n/**\r\n * deepmerge array merging algorithm\r\n * https://github.com/KyleAMathews/deepmerge#combine-array\r\n */\n\nfunction arrayMerge(target, source, options) {\n  var destination = target.slice();\n  source.forEach(function merge(e, i) {\n    if (typeof destination[i] === 'undefined') {\n      var cloneRequested = options.clone !== false;\n      var shouldClone = cloneRequested && options.isMergeableObject(e);\n      destination[i] = shouldClone ? (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Array.isArray(e) ? [] : {}, e, options) : e;\n    } else if (options.isMergeableObject(e)) {\n      destination[i] = (0,deepmerge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(target[i], e, options);\n    } else if (target.indexOf(e) === -1) {\n      destination.push(e);\n    }\n  });\n  return destination;\n}\n/** Return multi select values based on an array of options */\n\n\nfunction getSelectedValues(options) {\n  return Array.from(options).filter(function (el) {\n    return el.selected;\n  }).map(function (el) {\n    return el.value;\n  });\n}\n/** Return the next value for a checkbox */\n\n\nfunction getValueForCheckbox(currentValue, checked, valueProp) {\n  // If the current value was a boolean, return a boolean\n  if (typeof currentValue === 'boolean') {\n    return Boolean(checked);\n  } // If the currentValue was not a boolean we want to return an array\n\n\n  var currentArrayOfValues = [];\n  var isValueInArray = false;\n  var index = -1;\n\n  if (!Array.isArray(currentValue)) {\n    // eslint-disable-next-line eqeqeq\n    if (!valueProp || valueProp == 'true' || valueProp == 'false') {\n      return Boolean(checked);\n    }\n  } else {\n    // If the current value is already an array, use it\n    currentArrayOfValues = currentValue;\n    index = currentValue.indexOf(valueProp);\n    isValueInArray = index >= 0;\n  } // If the checkbox was checked and the value is not already present in the aray we want to add the new value to the array of values\n\n\n  if (checked && valueProp && !isValueInArray) {\n    return currentArrayOfValues.concat(valueProp);\n  } // If the checkbox was unchecked and the value is not in the array, simply return the already existing array of values\n\n\n  if (!isValueInArray) {\n    return currentArrayOfValues;\n  } // If the checkbox was unchecked and the value is in the array, remove the value and return the array\n\n\n  return currentArrayOfValues.slice(0, index).concat(currentArrayOfValues.slice(index + 1));\n} // React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\n// @see https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85\n\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_1__.useEffect;\n\nfunction useEventCallback(fn) {\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(fn); // we copy a ref to the callback scoped to the current state/props on each render\n\n  useIsomorphicLayoutEffect(function () {\n    ref.current = fn;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return ref.current.apply(void 0, args);\n  }, []);\n}\n\nfunction useField(propsOrFieldName) {\n  var formik = useFormikContext();\n  var getFieldProps = formik.getFieldProps,\n      getFieldMeta = formik.getFieldMeta,\n      getFieldHelpers = formik.getFieldHelpers,\n      registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  var isAnObject = isObject(propsOrFieldName); // Normalize propsOrFieldName to FieldHookConfig<Val>\n\n  var props = isAnObject ? propsOrFieldName : {\n    name: propsOrFieldName\n  };\n  var fieldName = props.name,\n      validateFn = props.validate;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    if (fieldName) {\n      registerField(fieldName, {\n        validate: validateFn\n      });\n    }\n\n    return function () {\n      if (fieldName) {\n        unregisterField(fieldName);\n      }\n    };\n  }, [registerField, unregisterField, fieldName, validateFn]);\n\n  if (true) {\n    !formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'useField() / <Field /> must be used underneath a <Formik> component or withFormik() higher order component') : 0 : void 0;\n  }\n\n  !fieldName ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'Invalid field name. Either pass `useField` a string or an object containing a `name` key.') : 0 : void 0;\n  var fieldHelpers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return getFieldHelpers(fieldName);\n  }, [getFieldHelpers, fieldName]);\n  return [getFieldProps(props), getFieldMeta(fieldName), fieldHelpers];\n}\nfunction Field(_ref) {\n  var validate = _ref.validate,\n      name = _ref.name,\n      render = _ref.render,\n      children = _ref.children,\n      is = _ref.as,\n      component = _ref.component,\n      className = _ref.className,\n      props = _objectWithoutPropertiesLoose(_ref, [\"validate\", \"name\", \"render\", \"children\", \"as\", \"component\", \"className\"]);\n\n  var _useFormikContext = useFormikContext(),\n      formik = _objectWithoutPropertiesLoose(_useFormikContext, [\"validate\", \"validationSchema\"]);\n\n  if (true) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<Field render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Field name=\\\"\" + name + \"\\\" render={({field, form}) => ...} /> with <Field name=\\\"\" + name + \"\\\">{({field, form, meta}) => ...}</Field>\") : 0 : void 0;\n      !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field as> and <Field children> as a function in the same <Field> component; <Field as> will be ignored.') : 0 : void 0;\n      !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field component> and <Field children> as a function in the same <Field> component; <Field component> will be ignored.') : 0 : void 0;\n      !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <Field render> and <Field children> in the same <Field> component; <Field children> will be ignored') : 0 : void 0; // eslint-disable-next-line\n    }, []);\n  } // Register field and field-level validation with parent <Formik>\n\n\n  var registerField = formik.registerField,\n      unregisterField = formik.unregisterField;\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    registerField(name, {\n      validate: validate\n    });\n    return function () {\n      unregisterField(name);\n    };\n  }, [registerField, unregisterField, name, validate]);\n  var field = formik.getFieldProps(_extends({\n    name: name\n  }, props));\n  var meta = formik.getFieldMeta(name);\n  var legacyBag = {\n    field: field,\n    form: formik\n  };\n\n  if (render) {\n    return render(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (isFunction(children)) {\n    return children(_extends({}, legacyBag, {\n      meta: meta\n    }));\n  }\n\n  if (component) {\n    // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n    if (typeof component === 'string') {\n      var innerRef = props.innerRef,\n          rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        ref: innerRef\n      }, field, rest, {\n        className: className\n      }), children);\n    } // We don't pass `meta` for backwards compat\n\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n      field: field,\n      form: formik\n    }, props, {\n      className: className\n    }), children);\n  } // default to input here so we can check for both `as` and `children` above\n\n\n  var asElement = is || 'input';\n\n  if (typeof asElement === 'string') {\n    var _innerRef = props.innerRef,\n        _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n      ref: _innerRef\n    }, field, _rest, {\n      className: className\n    }), children);\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props, {\n    className: className\n  }), children);\n}\n\nvar Form = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {\n  // iOS needs an \"action\" attribute for nice input: https://stackoverflow.com/a/39485162/406725\n  // We default the action to \"#\" in case the preventDefault fails (just updates the URL hash)\n  var action = props.action,\n      rest = _objectWithoutPropertiesLoose(props, [\"action\"]);\n\n  var _action = action != null ? action : '#';\n\n  var _useFormikContext = useFormikContext(),\n      handleReset = _useFormikContext.handleReset,\n      handleSubmit = _useFormikContext.handleSubmit;\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(\"form\", _extends({\n    onSubmit: handleSubmit,\n    ref: ref,\n    onReset: handleReset,\n    action: _action\n  }, rest));\n});\nForm.displayName = 'Form';\n\n/**\r\n * A public higher-order component to access the imperative API\r\n */\n\nfunction withFormik(_ref) {\n  var _ref$mapPropsToValues = _ref.mapPropsToValues,\n      mapPropsToValues = _ref$mapPropsToValues === void 0 ? function (vanillaProps) {\n    var val = {};\n\n    for (var k in vanillaProps) {\n      if (vanillaProps.hasOwnProperty(k) && typeof vanillaProps[k] !== 'function') {\n        // @todo TypeScript fix\n        val[k] = vanillaProps[k];\n      }\n    }\n\n    return val;\n  } : _ref$mapPropsToValues,\n      config = _objectWithoutPropertiesLoose(_ref, [\"mapPropsToValues\"]);\n\n  return function createFormik(Component$1) {\n    var componentDisplayName = Component$1.displayName || Component$1.name || Component$1.constructor && Component$1.constructor.name || 'Component';\n    /**\r\n     * We need to use closures here for to provide the wrapped component's props to\r\n     * the respective withFormik config methods.\r\n     */\n\n    var C = /*#__PURE__*/function (_React$Component) {\n      _inheritsLoose(C, _React$Component);\n\n      function C() {\n        var _this;\n\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n        _this.validate = function (values) {\n          return config.validate(values, _this.props);\n        };\n\n        _this.validationSchema = function () {\n          return isFunction(config.validationSchema) ? config.validationSchema(_this.props) : config.validationSchema;\n        };\n\n        _this.handleSubmit = function (values, actions) {\n          return config.handleSubmit(values, _extends({}, actions, {\n            props: _this.props\n          }));\n        };\n\n        _this.renderFormComponent = function (formikProps) {\n          return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Component$1, _extends({}, _this.props, formikProps));\n        };\n\n        return _this;\n      }\n\n      var _proto = C.prototype;\n\n      _proto.render = function render() {\n        var _this$props = this.props,\n            props = _objectWithoutPropertiesLoose(_this$props, [\"children\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Formik, _extends({}, props, config, {\n          validate: config.validate && this.validate,\n          validationSchema: config.validationSchema && this.validationSchema,\n          initialValues: mapPropsToValues(this.props),\n          initialStatus: config.mapPropsToStatus && config.mapPropsToStatus(this.props),\n          initialErrors: config.mapPropsToErrors && config.mapPropsToErrors(this.props),\n          initialTouched: config.mapPropsToTouched && config.mapPropsToTouched(this.props),\n          onSubmit: this.handleSubmit,\n          children: this.renderFormComponent\n        }));\n      };\n\n      return C;\n    }(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\n    C.displayName = \"WithFormik(\" + componentDisplayName + \")\";\n    return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Component$1 // cast type to ComponentClass (even if SFC)\n    );\n  };\n}\n\n/**\r\n * Connect any component to Formik context, and inject as a prop called `formik`;\r\n * @param Comp React Component\r\n */\n\nfunction connect(Comp) {\n  var C = function C(props) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(FormikConsumer, null, function (formik) {\n      !!!formik ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"Formik context is undefined, please verify you are rendering <Form>, <Field>, <FastField>, <FieldArray>, or your custom context-using component as a child of a <Formik> component. Component name: \" + Comp.name) : 0 : void 0;\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(Comp, _extends({}, props, {\n        formik: formik\n      }));\n    });\n  };\n\n  var componentDisplayName = Comp.displayName || Comp.name || Comp.constructor && Comp.constructor.name || 'Component'; // Assign Comp to C.WrappedComponent so we can access the inner component in tests\n  // For example, <Field.WrappedComponent /> gets us <FieldInner/>\n\n  C.WrappedComponent = Comp;\n  C.displayName = \"FormikConnect(\" + componentDisplayName + \")\";\n  return hoist_non_react_statics__WEBPACK_IMPORTED_MODULE_3___default()(C, Comp // cast type to ComponentClass (even if SFC)\n  );\n}\n\n/**\r\n * Some array helpers!\r\n */\n\nvar move = function move(array, from, to) {\n  var copy = copyArrayLike(array);\n  var value = copy[from];\n  copy.splice(from, 1);\n  copy.splice(to, 0, value);\n  return copy;\n};\nvar swap = function swap(arrayLike, indexA, indexB) {\n  var copy = copyArrayLike(arrayLike);\n  var a = copy[indexA];\n  copy[indexA] = copy[indexB];\n  copy[indexB] = a;\n  return copy;\n};\nvar insert = function insert(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy.splice(index, 0, value);\n  return copy;\n};\nvar replace = function replace(arrayLike, index, value) {\n  var copy = copyArrayLike(arrayLike);\n  copy[index] = value;\n  return copy;\n};\n\nvar copyArrayLike = function copyArrayLike(arrayLike) {\n  if (!arrayLike) {\n    return [];\n  } else if (Array.isArray(arrayLike)) {\n    return [].concat(arrayLike);\n  } else {\n    var maxIndex = Object.keys(arrayLike).map(function (key) {\n      return parseInt(key);\n    }).reduce(function (max, el) {\n      return el > max ? el : max;\n    }, 0);\n    return Array.from(_extends({}, arrayLike, {\n      length: maxIndex + 1\n    }));\n  }\n};\n\nvar createAlterationHandler = function createAlterationHandler(alteration, defaultFunction) {\n  var fn = typeof alteration === 'function' ? alteration : defaultFunction;\n  return function (data) {\n    if (Array.isArray(data) || isObject(data)) {\n      var clone = copyArrayLike(data);\n      return fn(clone);\n    } // This can be assumed to be a primitive, which\n    // is a case for top level validation errors\n\n\n    return data;\n  };\n};\n\nvar FieldArrayInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FieldArrayInner, _React$Component);\n\n  function FieldArrayInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this; // We need TypeScript generics on these, so we'll bind them in the constructor\n    // @todo Fix TS 3.2.1\n\n    _this.updateArrayField = function (fn, alterTouched, alterErrors) {\n      var _this$props = _this.props,\n          name = _this$props.name,\n          setFormikState = _this$props.formik.setFormikState;\n      setFormikState(function (prevState) {\n        var updateErrors = createAlterationHandler(alterErrors, fn);\n        var updateTouched = createAlterationHandler(alterTouched, fn); // values fn should be executed before updateErrors and updateTouched,\n        // otherwise it causes an error with unshift.\n\n        var values = setIn(prevState.values, name, fn(getIn(prevState.values, name)));\n        var fieldError = alterErrors ? updateErrors(getIn(prevState.errors, name)) : undefined;\n        var fieldTouched = alterTouched ? updateTouched(getIn(prevState.touched, name)) : undefined;\n\n        if (isEmptyArray(fieldError)) {\n          fieldError = undefined;\n        }\n\n        if (isEmptyArray(fieldTouched)) {\n          fieldTouched = undefined;\n        }\n\n        return _extends({}, prevState, {\n          values: values,\n          errors: alterErrors ? setIn(prevState.errors, name, fieldError) : prevState.errors,\n          touched: alterTouched ? setIn(prevState.touched, name, fieldTouched) : prevState.touched\n        });\n      });\n    };\n\n    _this.push = function (value) {\n      return _this.updateArrayField(function (arrayLike) {\n        return [].concat(copyArrayLike(arrayLike), [(0,lodash_es_cloneDeep__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value)]);\n      }, false, false);\n    };\n\n    _this.handlePush = function (value) {\n      return function () {\n        return _this.push(value);\n      };\n    };\n\n    _this.swap = function (indexA, indexB) {\n      return _this.updateArrayField(function (array) {\n        return swap(array, indexA, indexB);\n      }, true, true);\n    };\n\n    _this.handleSwap = function (indexA, indexB) {\n      return function () {\n        return _this.swap(indexA, indexB);\n      };\n    };\n\n    _this.move = function (from, to) {\n      return _this.updateArrayField(function (array) {\n        return move(array, from, to);\n      }, true, true);\n    };\n\n    _this.handleMove = function (from, to) {\n      return function () {\n        return _this.move(from, to);\n      };\n    };\n\n    _this.insert = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return insert(array, index, value);\n      }, function (array) {\n        return insert(array, index, null);\n      }, function (array) {\n        return insert(array, index, null);\n      });\n    };\n\n    _this.handleInsert = function (index, value) {\n      return function () {\n        return _this.insert(index, value);\n      };\n    };\n\n    _this.replace = function (index, value) {\n      return _this.updateArrayField(function (array) {\n        return replace(array, index, value);\n      }, false, false);\n    };\n\n    _this.handleReplace = function (index, value) {\n      return function () {\n        return _this.replace(index, value);\n      };\n    };\n\n    _this.unshift = function (value) {\n      var length = -1;\n\n      _this.updateArrayField(function (array) {\n        var arr = array ? [value].concat(array) : [value];\n        length = arr.length;\n        return arr;\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      }, function (array) {\n        return array ? [null].concat(array) : [null];\n      });\n\n      return length;\n    };\n\n    _this.handleUnshift = function (value) {\n      return function () {\n        return _this.unshift(value);\n      };\n    };\n\n    _this.handleRemove = function (index) {\n      return function () {\n        return _this.remove(index);\n      };\n    };\n\n    _this.handlePop = function () {\n      return function () {\n        return _this.pop();\n      };\n    };\n\n    _this.remove = _this.remove.bind(_assertThisInitialized(_this));\n    _this.pop = _this.pop.bind(_assertThisInitialized(_this));\n    return _this;\n  }\n\n  var _proto = FieldArrayInner.prototype;\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.validateOnChange && this.props.formik.validateOnChange && !react_fast_compare__WEBPACK_IMPORTED_MODULE_2___default()(getIn(prevProps.formik.values, prevProps.name), getIn(this.props.formik.values, this.props.name))) {\n      this.props.formik.validateForm(this.props.formik.values);\n    }\n  };\n\n  _proto.remove = function remove(index) {\n    // We need to make sure we also remove relevant pieces of `touched` and `errors`\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var copy = array ? copyArrayLike(array) : [];\n\n      if (!result) {\n        result = copy[index];\n      }\n\n      if (isFunction(copy.splice)) {\n        copy.splice(index, 1);\n      } // if the array only includes undefined values we have to return an empty array\n\n\n      return isFunction(copy.every) ? copy.every(function (v) {\n        return v === undefined;\n      }) ? [] : copy : copy;\n    }, true, true);\n    return result;\n  };\n\n  _proto.pop = function pop() {\n    // Remove relevant pieces of `touched` and `errors` too!\n    var result;\n    this.updateArrayField( // so this gets call 3 times\n    function (array) {\n      var tmp = array.slice();\n\n      if (!result) {\n        result = tmp && tmp.pop && tmp.pop();\n      }\n\n      return tmp;\n    }, true, true);\n    return result;\n  };\n\n  _proto.render = function render() {\n    var arrayHelpers = {\n      push: this.push,\n      pop: this.pop,\n      swap: this.swap,\n      move: this.move,\n      insert: this.insert,\n      replace: this.replace,\n      unshift: this.unshift,\n      remove: this.remove,\n      handlePush: this.handlePush,\n      handlePop: this.handlePop,\n      handleSwap: this.handleSwap,\n      handleMove: this.handleMove,\n      handleInsert: this.handleInsert,\n      handleReplace: this.handleReplace,\n      handleUnshift: this.handleUnshift,\n      handleRemove: this.handleRemove\n    };\n\n    var _this$props2 = this.props,\n        component = _this$props2.component,\n        render = _this$props2.render,\n        children = _this$props2.children,\n        name = _this$props2.name,\n        _this$props2$formik = _this$props2.formik,\n        restOfFormik = _objectWithoutPropertiesLoose(_this$props2$formik, [\"validate\", \"validationSchema\"]);\n\n    var props = _extends({}, arrayHelpers, {\n      form: restOfFormik,\n      name: name\n    });\n\n    return component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, props) : render ? render(props) : children // children come last, always called\n    ? typeof children === 'function' ? children(props) : !isEmptyChildren(children) ? react__WEBPACK_IMPORTED_MODULE_1__.Children.only(children) : null : null;\n  };\n\n  return FieldArrayInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nFieldArrayInner.defaultProps = {\n  validateOnChange: true\n};\nvar FieldArray = /*#__PURE__*/connect(FieldArrayInner);\n\nvar ErrorMessageImpl = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorMessageImpl, _React$Component);\n\n  function ErrorMessageImpl() {\n    return _React$Component.apply(this, arguments) || this;\n  }\n\n  var _proto = ErrorMessageImpl.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (getIn(this.props.formik.errors, this.props.name) !== getIn(props.formik.errors, this.props.name) || getIn(this.props.formik.touched, this.props.name) !== getIn(props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        render = _this$props.render,\n        children = _this$props.children,\n        name = _this$props.name,\n        rest = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"formik\", \"render\", \"children\", \"name\"]);\n\n    var touch = getIn(formik.touched, name);\n    var error = getIn(formik.errors, name);\n    return !!touch && !!error ? render ? isFunction(render) ? render(error) : null : children ? isFunction(children) ? children(error) : null : component ? (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, rest, error) : error : null;\n  };\n\n  return ErrorMessageImpl;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar ErrorMessage = /*#__PURE__*/connect(ErrorMessageImpl);\n\n/**\r\n * Custom Field component for quickly hooking into Formik\r\n * context and wiring up forms.\r\n */\n\nvar FastFieldInner = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(FastFieldInner, _React$Component);\n\n  function FastFieldInner(props) {\n    var _this;\n\n    _this = _React$Component.call(this, props) || this;\n    var render = props.render,\n        children = props.children,\n        component = props.component,\n        is = props.as,\n        name = props.name;\n    !!render ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, \"<FastField render> has been deprecated. Please use a child callback function instead: <FastField name={\" + name + \"}>{props => ...}</FastField> instead.\") : 0 : void 0;\n    !!(component && render) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField render> in the same <FastField> component; <FastField component> will be ignored') : 0 : void 0;\n    !!(is && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField as> and <FastField children> as a function in the same <FastField> component; <FastField as> will be ignored.') : 0 : void 0;\n    !!(component && children && isFunction(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField component> and <FastField children> as a function in the same <FastField> component; <FastField component> will be ignored.') : 0 : void 0;\n    !!(render && children && !isEmptyChildren(children)) ?  true ? (0,tiny_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(false, 'You should not use <FastField render> and <FastField children> in the same <FastField> component; <FastField children> will be ignored') : 0 : void 0;\n    return _this;\n  }\n\n  var _proto = FastFieldInner.prototype;\n\n  _proto.shouldComponentUpdate = function shouldComponentUpdate(props) {\n    if (this.props.shouldUpdate) {\n      return this.props.shouldUpdate(props, this.props);\n    } else if (props.name !== this.props.name || getIn(props.formik.values, this.props.name) !== getIn(this.props.formik.values, this.props.name) || getIn(props.formik.errors, this.props.name) !== getIn(this.props.formik.errors, this.props.name) || getIn(props.formik.touched, this.props.name) !== getIn(this.props.formik.touched, this.props.name) || Object.keys(this.props).length !== Object.keys(props).length || props.formik.isSubmitting !== this.props.formik.isSubmitting) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  _proto.componentDidMount = function componentDidMount() {\n    // Register the Field with the parent Formik. Parent will cycle through\n    // registered Field's validate fns right prior to submit\n    this.props.formik.registerField(this.props.name, {\n      validate: this.props.validate\n    });\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps) {\n    if (this.props.name !== prevProps.name) {\n      this.props.formik.unregisterField(prevProps.name);\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n\n    if (this.props.validate !== prevProps.validate) {\n      this.props.formik.registerField(this.props.name, {\n        validate: this.props.validate\n      });\n    }\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.props.formik.unregisterField(this.props.name);\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        name = _this$props.name,\n        render = _this$props.render,\n        is = _this$props.as,\n        children = _this$props.children,\n        component = _this$props.component,\n        formik = _this$props.formik,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"validate\", \"name\", \"render\", \"as\", \"children\", \"component\", \"shouldUpdate\", \"formik\"]);\n\n    var restOfFormik = _objectWithoutPropertiesLoose(formik, [\"validate\", \"validationSchema\"]);\n\n    var field = formik.getFieldProps(_extends({\n      name: name\n    }, props));\n    var meta = {\n      value: getIn(formik.values, name),\n      error: getIn(formik.errors, name),\n      touched: !!getIn(formik.touched, name),\n      initialValue: getIn(formik.initialValues, name),\n      initialTouched: !!getIn(formik.initialTouched, name),\n      initialError: getIn(formik.initialErrors, name)\n    };\n    var bag = {\n      field: field,\n      meta: meta,\n      form: restOfFormik\n    };\n\n    if (render) {\n      return render(bag);\n    }\n\n    if (isFunction(children)) {\n      return children(bag);\n    }\n\n    if (component) {\n      // This behavior is backwards compat with earlier Formik 0.9 to 1.x\n      if (typeof component === 'string') {\n        var innerRef = props.innerRef,\n            rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n        return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n          ref: innerRef\n        }, field, rest), children);\n      } // We don't pass `meta` for backwards compat\n\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(component, _extends({\n        field: field,\n        form: formik\n      }, props), children);\n    } // default to input here so we can check for both `as` and `children` above\n\n\n    var asElement = is || 'input';\n\n    if (typeof asElement === 'string') {\n      var _innerRef = props.innerRef,\n          _rest = _objectWithoutPropertiesLoose(props, [\"innerRef\"]);\n\n      return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({\n        ref: _innerRef\n      }, field, _rest), children);\n    }\n\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(asElement, _extends({}, field, props), children);\n  };\n\n  return FastFieldInner;\n}(react__WEBPACK_IMPORTED_MODULE_1__.Component);\n\nvar FastField = /*#__PURE__*/connect(FastFieldInner);\n\n\n//# sourceMappingURL=formik.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/formik/dist/formik.esm.js\n");

/***/ })

};
;