"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import MainHOF from "../../layout/MainHOF";
import { Button } from "../../components/ui/button";
import { useToast } from "../../components/ui/use-toast";
import useApi from "@/hooks/useApi";
import { MAIN_URL, ORDERS } from "@/constant/urls";
import { useSession } from "next-auth/react";
import axios from "axios";
import SpinnerLoader from "@/components/ui/loading/SpinnerLoader";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Download, ShoppingBag, Truck } from "lucide-react";
import Link from "next/link";

const OrderDetailsContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("order_id");
  const { toast } = useToast();
  const { data: session, status } = useSession();
  const { read, data: orderData, loading } = useApi(MAIN_URL);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [downloadingInvoice, setDownloadingInvoice] = useState(false);

  useEffect(() => {
    if (!orderId) {
      toast({
        title: "Error",
        description: "No order ID provided",
      });
      router.push("/");
      return;
    }

    if (status === "authenticated") {
      fetchOrderDetails();
    } else if (status === "unauthenticated") {
      router.push("/auth/login");
    }
  }, [orderId, status]);

  const fetchOrderDetails = async () => {
    try {
      const data = await read(`${ORDERS}${orderId}/`);
      setOrderDetails(data);
    } catch (error) {
      console.error("Error fetching order details:", error);
      toast({
        title: "Error",
        description: "Could not fetch order details. Please try again later.",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case "PAID":
        return "bg-green-100 text-green-800";
      case "PROCESSING":
        return "bg-blue-100 text-blue-800";
      case "SHIPPED":
        return "bg-purple-100 text-purple-800";
      case "DELIVERED":
        return "bg-green-100 text-green-800";
      case "CANCELLED":
        return "bg-red-100 text-red-800";
      case "REFUNDED":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleDownloadInvoice = async (orderId: string) => {
    if (!orderId) return;

    setDownloadingInvoice(true);
    try {
      // Create a direct download link with authentication
      const downloadUrl = `${MAIN_URL}${ORDERS}${orderId}/invoice/download/`;

      // Get the access token from session (same way useApi does it)
      if (status !== "authenticated" || !session?.user?.access) {
        toast({
          title: "Authentication Error",
          description: "Please log in to download the invoice.",
          variant: "destructive",
        });
        return;
      }

      const accessToken = session.user.access;

      // Use axios with proper authentication headers and response type
      const response = await axios({
        method: 'GET',
        url: downloadUrl,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
        responseType: 'blob', // Important for PDF download
      });

      // Create blob URL and trigger download
      const blob = new Blob([response.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice_${orderId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Invoice downloaded successfully!",
      });
    } catch (error: any) {
      console.error('Error downloading invoice:', error);

      if (error.response?.status === 401) {
        toast({
          title: "Authentication Error",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        router.push("/auth/login");
      } else if (error.response?.status === 404) {
        toast({
          title: "Invoice Not Found",
          description: "Invoice not found for this order.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: error.response?.data?.detail || "Failed to download invoice. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setDownloadingInvoice(false);
    }
  };

  if (loading || !orderDetails) {
    return (
      <MainHOF>
        <div className="container h-96 flex justify-center items-center mx-auto px-4 py-8">
          <SpinnerLoader />
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.back()}
              className="mr-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold">Order Details</h1>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-sm mb-6">
            <div className="flex flex-wrap justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-semibold">Order #{orderDetails?.id}</h2>
                <p className="text-muted-foreground">
                  Placed on {formatDate(orderDetails?.created_at)}
                </p>
              </div>
              <div>
                <span
                  className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusClass(
                    orderDetails?.status
                  )}`}
                >
                  {orderDetails?.status}
                </span>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium mb-2">Shipping Address</h3>
                <p className="text-sm">
                  {orderDetails?.shipping_address?.street_address}
                  <br />
                  {orderDetails?.shipping_address?.city},{" "}
                  {orderDetails?.shipping_address?.state}{" "}
                  {orderDetails?.shipping_address?.postal_code}
                  <br />
                  {orderDetails?.shipping_address?.country}
                </p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Billing Address</h3>
                <p className="text-sm">
                  {orderDetails?.billing_address?.street_address}
                  <br />
                  {orderDetails?.billing_address?.city},{" "}
                  {orderDetails?.billing_address?.state}{" "}
                  {orderDetails?.billing_address?.postal_code}
                  <br />
                  {orderDetails?.billing_address?.country}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-sm mb-6">
            <h2 className="text-xl font-semibold mb-4">Order Items</h2>
            <div className="space-y-4">
              {Array.isArray(orderDetails?.items) &&
                orderDetails?.items.map((item: any) => (
                  <div key={item.id} className="flex items-center gap-4 border-b pb-4">
                    <img
                      src={item.product_image}
                      alt={item.product_name}
                      className="w-20 h-20 object-cover rounded"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium">{item.product_name}</h3>
                      {item.variant_name && (
                        <p className="text-sm text-muted-foreground">
                          Variant: {item.variant_name}
                        </p>
                      )}
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        ₹{Number(item.total_price).toFixed(2)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        ₹{Number(item.unit_price).toFixed(2)} each
                      </p>
                    </div>
                  </div>
                ))}
            </div>

            <Separator className="my-4" />

            <div className="space-y-2 ml-auto w-full md:w-1/2 md:ml-auto">
              <div className="flex justify-between">
                <span>Subtotal (before GST):</span>
                <span>₹{Number(orderDetails?.subtotal).toFixed(2)}</span>
              </div>

              {/* GST Breakdown */}
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>GST ({orderDetails?.gst_amount ? 'Dynamic' : '18%'}):</span>
                  <span>₹{(Number(orderDetails?.gst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.18).toFixed(2)}</span>
                </div>

                {/* Transaction Type Indicator */}
                <div className="ml-4 text-xs font-medium text-blue-600 mb-1">
                  {Number(orderDetails?.igst_amount || 0) > 0
                    ? '🔄 Inter-state Transaction'
                    : '🏠 Intra-state Transaction'}
                </div>

                <div className="ml-4 space-y-1 text-sm text-gray-600">
                  {/* Apply Indian GST rules */}
                  {Number(orderDetails?.igst_amount || 0) > 0 ? (
                    // Inter-state: Only IGST
                    <div className="flex justify-between">
                      <span>IGST ({orderDetails?.gst_amount ? 'Dynamic' : '18'}%):</span>
                      <span>₹{Number(orderDetails?.igst_amount || 0).toFixed(2)}</span>
                    </div>
                  ) : (
                    // Intra-state: CGST + SGST (split equally)
                    <>
                      <div className="flex justify-between">
                        <span>CGST ({orderDetails?.gst_amount ? 'Dynamic' : '9'}%):</span>
                        <span>₹{(Number(orderDetails?.cgst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.09).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>SGST ({orderDetails?.gst_amount ? 'Dynamic' : '9'}%):</span>
                        <span>₹{(Number(orderDetails?.sgst_amount || 0) || Number(orderDetails?.subtotal || 0) * 0.09).toFixed(2)}</span>
                      </div>
                    </>
                  )}
                </div>

                {/* Address-based transaction type display */}
                <div className="ml-4 text-xs text-gray-500 mt-2">
                  <div className="flex justify-between">
                    <span>Billing State:</span>
                    <span>{orderDetails?.billing_address?.state}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Shipping State:</span>
                    <span>{orderDetails?.shipping_address?.state}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-between">
                <span>Shipping:</span>
                <span>₹{Number(orderDetails?.shipping_cost).toFixed(2)}</span>
              </div>
              {orderDetails?.promo_discount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount:</span>
                  <span>-₹{Number(orderDetails?.promo_discount).toFixed(2)}</span>
                </div>
              )}
              <Separator className="my-2" />
              <div className="flex justify-between font-bold text-lg">
                <span>Total Amount:</span>
                <span>₹{Number(orderDetails?.total).toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {orderDetails?.status === "SHIPPED" && (
              <Button className="flex items-center gap-2">
                <Truck className="h-4 w-4" />
                Track Order
              </Button>
            )}
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => handleDownloadInvoice(orderDetails?.id)}
              disabled={downloadingInvoice}
            >
              {downloadingInvoice ? (
                <>
                  <div className="w-4 h-4 border-t-2 border-b-2 border-current rounded-full animate-spin"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  Download Invoice
                </>
              )}
            </Button>
            <Link href="/shop">
              <Button variant="secondary" className="flex items-center gap-2">
                <ShoppingBag className="h-4 w-4" />
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

const OrderDetails = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OrderDetailsContent />
    </Suspense>
  );
};

export default OrderDetails;
