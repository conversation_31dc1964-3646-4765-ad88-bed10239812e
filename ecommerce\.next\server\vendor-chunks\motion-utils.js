"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/motion-utils";
exports.ids = ["vendor-chunks/motion-utils"];
exports.modules = {

/***/ "(ssr)/./node_modules/motion-utils/dist/es/errors.mjs":
/*!******************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/errors.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant),\n/* harmony export */   warning: () => (/* binding */ warning)\n/* harmony export */ });\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./noop.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/noop.mjs\");\n\n\nlet warning = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nlet invariant = _noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop;\nif (true) {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvZXJyb3JzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7O0FBRWxDLGNBQWMsMkNBQUk7QUFDbEIsZ0JBQWdCLDJDQUFJO0FBQ3BCLElBQUksSUFBcUM7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFOEIiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcbW90aW9uLXV0aWxzXFxkaXN0XFxlc1xcZXJyb3JzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBub29wIH0gZnJvbSAnLi9ub29wLm1qcyc7XG5cbmxldCB3YXJuaW5nID0gbm9vcDtcbmxldCBpbnZhcmlhbnQgPSBub29wO1xuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHdhcm5pbmcgPSAoY2hlY2ssIG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKCFjaGVjayAmJiB0eXBlb2YgY29uc29sZSAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKG1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBpbnZhcmlhbnQgPSAoY2hlY2ssIG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKCFjaGVjaykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpO1xuICAgICAgICB9XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgaW52YXJpYW50LCB3YXJuaW5nIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/errors.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* reexport safe */ _errors_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant),\n/* harmony export */   memo: () => (/* reexport safe */ _memo_mjs__WEBPACK_IMPORTED_MODULE_1__.memo),\n/* harmony export */   millisecondsToSeconds: () => (/* reexport safe */ _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__.millisecondsToSeconds),\n/* harmony export */   noop: () => (/* reexport safe */ _noop_mjs__WEBPACK_IMPORTED_MODULE_2__.noop),\n/* harmony export */   progress: () => (/* reexport safe */ _progress_mjs__WEBPACK_IMPORTED_MODULE_3__.progress),\n/* harmony export */   secondsToMilliseconds: () => (/* reexport safe */ _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__.secondsToMilliseconds),\n/* harmony export */   warning: () => (/* reexport safe */ _errors_mjs__WEBPACK_IMPORTED_MODULE_0__.warning)\n/* harmony export */ });\n/* harmony import */ var _errors_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./errors.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var _memo_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./memo.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/memo.mjs\");\n/* harmony import */ var _noop_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./noop.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _progress_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./progress.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var _time_conversion_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./time-conversion.mjs */ \"(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNoQjtBQUNBO0FBQ1E7QUFDMkMiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcbW90aW9uLXV0aWxzXFxkaXN0XFxlc1xcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGludmFyaWFudCwgd2FybmluZyB9IGZyb20gJy4vZXJyb3JzLm1qcyc7XG5leHBvcnQgeyBtZW1vIH0gZnJvbSAnLi9tZW1vLm1qcyc7XG5leHBvcnQgeyBub29wIH0gZnJvbSAnLi9ub29wLm1qcyc7XG5leHBvcnQgeyBwcm9ncmVzcyB9IGZyb20gJy4vcHJvZ3Jlc3MubWpzJztcbmV4cG9ydCB7IG1pbGxpc2Vjb25kc1RvU2Vjb25kcywgc2Vjb25kc1RvTWlsbGlzZWNvbmRzIH0gZnJvbSAnLi90aW1lLWNvbnZlcnNpb24ubWpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/memo.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/memo.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memo: () => (/* binding */ memo)\n/* harmony export */ });\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbWVtby5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWdCIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG1vdGlvbi11dGlsc1xcZGlzdFxcZXNcXG1lbW8ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIG1lbW8oY2FsbGJhY2spIHtcbiAgICBsZXQgcmVzdWx0O1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGlmIChyZXN1bHQgPT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgIHJlc3VsdCA9IGNhbGxiYWNrKCk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgbWVtbyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/memo.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/noop.mjs":
/*!****************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/noop.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop)\n/* harmony export */ });\nconst noop = (any) => any;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvbm9vcC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUVnQiIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tdXRpbHNcXGRpc3RcXGVzXFxub29wLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBub29wID0gKGFueSkgPT4gYW55O1xuXG5leHBvcnQgeyBub29wIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/noop.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/progress.mjs":
/*!********************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/progress.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   progress: () => (/* binding */ progress)\n/* harmony export */ });\n/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvcHJvZ3Jlc3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG1vdGlvbi11dGlsc1xcZGlzdFxcZXNcXHByb2dyZXNzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKlxuICBQcm9ncmVzcyB3aXRoaW4gZ2l2ZW4gcmFuZ2VcblxuICBHaXZlbiBhIGxvd2VyIGxpbWl0IGFuZCBhbiB1cHBlciBsaW1pdCwgd2UgcmV0dXJuIHRoZSBwcm9ncmVzc1xuICAoZXhwcmVzc2VkIGFzIGEgbnVtYmVyIDAtMSkgcmVwcmVzZW50ZWQgYnkgdGhlIGdpdmVuIHZhbHVlLCBhbmRcbiAgbGltaXQgdGhhdCBwcm9ncmVzcyB0byB3aXRoaW4gMC0xLlxuXG4gIEBwYXJhbSBbbnVtYmVyXTogTG93ZXIgbGltaXRcbiAgQHBhcmFtIFtudW1iZXJdOiBVcHBlciBsaW1pdFxuICBAcGFyYW0gW251bWJlcl06IFZhbHVlIHRvIGZpbmQgcHJvZ3Jlc3Mgd2l0aGluIGdpdmVuIHJhbmdlXG4gIEByZXR1cm4gW251bWJlcl06IFByb2dyZXNzIG9mIHZhbHVlIHdpdGhpbiByYW5nZSBhcyBleHByZXNzZWQgMC0xXG4qL1xuY29uc3QgcHJvZ3Jlc3MgPSAoZnJvbSwgdG8sIHZhbHVlKSA9PiB7XG4gICAgY29uc3QgdG9Gcm9tRGlmZmVyZW5jZSA9IHRvIC0gZnJvbTtcbiAgICByZXR1cm4gdG9Gcm9tRGlmZmVyZW5jZSA9PT0gMCA/IDEgOiAodmFsdWUgLSBmcm9tKSAvIHRvRnJvbURpZmZlcmVuY2U7XG59O1xuXG5leHBvcnQgeyBwcm9ncmVzcyB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/progress.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/motion-utils/dist/es/time-conversion.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecondsToSeconds: () => (/* binding */ millisecondsToSeconds),\n/* harmony export */   secondsToMilliseconds: () => (/* binding */ secondsToMilliseconds)\n/* harmony export */ });\n/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbW90aW9uLXV0aWxzL2Rpc3QvZXMvdGltZS1jb252ZXJzaW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXdEIiwic291cmNlcyI6WyJEOlxcVHJpdW1waFxcZWNvbW1lcmNlXFxub2RlX21vZHVsZXNcXG1vdGlvbi11dGlsc1xcZGlzdFxcZXNcXHRpbWUtY29udmVyc2lvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb252ZXJ0cyBzZWNvbmRzIHRvIG1pbGxpc2Vjb25kc1xuICpcbiAqIEBwYXJhbSBzZWNvbmRzIC0gVGltZSBpbiBzZWNvbmRzLlxuICogQHJldHVybiBtaWxsaXNlY29uZHMgLSBDb252ZXJ0ZWQgdGltZSBpbiBtaWxsaXNlY29uZHMuXG4gKi9cbmNvbnN0IHNlY29uZHNUb01pbGxpc2Vjb25kcyA9IChzZWNvbmRzKSA9PiBzZWNvbmRzICogMTAwMDtcbmNvbnN0IG1pbGxpc2Vjb25kc1RvU2Vjb25kcyA9IChtaWxsaXNlY29uZHMpID0+IG1pbGxpc2Vjb25kcyAvIDEwMDA7XG5cbmV4cG9ydCB7IG1pbGxpc2Vjb25kc1RvU2Vjb25kcywgc2Vjb25kc1RvTWlsbGlzZWNvbmRzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/motion-utils/dist/es/time-conversion.mjs\n");

/***/ })

};
;